import http from '@/utils/http'

const BASE_URL = '/biz/transactionUser'

export interface TransactionUserResp {
  id: string
  transactionType: string
  referId: string
  name: string
  createUser: string
  createTime: string
  createUserString: string
  updateUserString: string
  disabled: boolean
}
export interface TransactionUserDetailResp {
  id: string
  transactionType: string
  referId: string
  name: string
  createUser: string
  createTime: string
  createUserString: string
  updateUserString: string
}
export interface TransactionUserQuery {
  transactionType: string | undefined
  name: string | undefined
  sort: Array<string>
}
export interface TransactionUserPageQuery extends TransactionUserQuery, PageQuery {}

/** @desc 查询交易对象列表 */
export function listTransactionUser(query: TransactionUserPageQuery) {
  return http.get<PageRes<TransactionUserResp[]>>(`${BASE_URL}`, query)
}

/** @desc 查询交易对象详情 */
export function getTransactionUser(id: string) {
  return http.get<TransactionUserDetailResp>(`${BASE_URL}/${id}`)
}

/** @desc 新增交易对象 */
export function addTransactionUser(data: any) {
  return http.post(`${BASE_URL}`, data)
}

/** @desc 修改交易对象 */
export function updateTransactionUser(data: any, id: string) {
  return http.put(`${BASE_URL}/${id}`, data)
}

/** @desc 删除交易对象 */
export function deleteTransactionUser(id: string) {
  return http.del(`${BASE_URL}/${id}`)
}

/** @desc 导出交易对象 */
export function exportTransactionUser(query: TransactionUserQuery) {
  return http.download(`${BASE_URL}/export`, query)
}
