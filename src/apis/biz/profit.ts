import http from '@/utils/http'

const BASE_URL = '/biz/profit'

export interface ProfitResp {
  id: string
  adPlatform: string
  project: string
  type: string
  amount: string
  transTime: string
  transactionHash: string
  remark: string
  createUserString: string
  updateUserString: string
  disabled: boolean
}
export interface ProfitDetailResp {
  id: string
  adPlatform: string
  project: string
  type: string
  amount: string
  transTime: string
  transactionHash: string
  remark: string
  createTime: string
  createUser: string
  createUserString: string
  updateUserString: string
}
export interface ProfitQuery {
  id: string | undefined
  adPlatform: string | undefined
  project: string | undefined
  type: string | undefined
  transTime: string | undefined
  transactionHash: string | undefined
  remark: string | undefined
  sort: Array<string>
}
export interface ProfitPageQuery extends ProfitQuery, PageQuery {}

/** @desc 查询利润列表 */
export function listProfit(query: ProfitPageQuery) {
  return http.get<PageRes<ProfitResp[]>>(`${BASE_URL}`, query)
}

/** @desc 查询利润详情 */
export function getProfit(id: string) {
  return http.get<ProfitDetailResp>(`${BASE_URL}/${id}`)
}

/** @desc 新增利润 */
export function addProfit(data: any) {
  return http.post(`${BASE_URL}`, data)
}

/** @desc 修改利润 */
export function updateProfit(data: any, id: string) {
  return http.put(`${BASE_URL}/${id}`, data)
}

/** @desc 删除利润 */
export function deleteProfit(id: string) {
  return http.del(`${BASE_URL}/${id}`)
}

/** @desc 导出利润 */
export function exportProfit(query: ProfitQuery) {
  return http.download(`${BASE_URL}/export`, query)
}
