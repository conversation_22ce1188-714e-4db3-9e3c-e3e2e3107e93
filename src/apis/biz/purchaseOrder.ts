import http from '@/utils/http'

const BASE_URL = '/biz/purchaseOrder'

export interface PurchaseOrderResp {
  id: string
  channelId: string
  type: string
  expectNum: string
  receiveNum: string
  totalPrice: string
  isPay: string
  payTime: string
  payPrice: number
  status: number
  finishTime: string
  remark: string
  createUser: string
  createTime: string
  adPlatform: number
  project: number
  openReceive: boolean
  createUserString: string
  updateUserString: string
  disabled: boolean
}
export interface PurchaseOrderDetailResp {
  id: string
  channelId: string
  type: string
  expectNum: string
  receiveNum: string
  totalPrice: string
  isPay: string
  payTime: string
  payPrice: string
  status: string
  finishTime: string
  remark: string
  createUser: string
  createTime: string
  createUserString: string
  updateUserString: string
}
export interface PurchaseOrderQuery {
  adPlatform: string | undefined
  project: string | undefined
  channelId: string | undefined
  type: string | undefined
  isPay: string | undefined
  status: string | undefined
  createTime: string | undefined
  purchaseTime: string | undefined
  sort: Array<string>
}
export interface PurchaseOrderPageQuery extends PurchaseOrderQuery, PageQuery {}

export interface PurchaseOrderCheckResp {
  channelName: string
  type: number
  purchaseNum: number
  purchasePrice: number
  writeNum: number
  writePrice: number
  message: string

}

/** @desc 查询采购订单列表 */
export function listPurchaseOrder(query: PurchaseOrderPageQuery) {
  return http.get<PageRes<PurchaseOrderResp[]>>(`${BASE_URL}`, query)
}

/** @desc 查询采购订单详情 */
export function getPurchaseOrder(id: string) {
  return http.get<PurchaseOrderDetailResp>(`${BASE_URL}/${id}`)
}

/** @desc 新增采购订单 */
export function addPurchaseOrder(data: any) {
  return http.post(`${BASE_URL}`, data)
}

/** @desc 修改采购订单 */
export function updatePurchaseOrder(data: any, id: string) {
  return http.put(`${BASE_URL}/${id}`, data)
}

/** @desc 删除采购订单 */
export function deletePurchaseOrder(id: string) {
  return http.del(`${BASE_URL}/${id}`)
}

/** @desc 导出采购订单 */
export function exportPurchaseOrder(query: PurchaseOrderQuery) {
  return http.download(`${BASE_URL}/export`, query)
}

/** @desc 支付采购订单 */
export function payPurchaseOrder(data: any) {
  return http.post(`${BASE_URL}/pay`, data)
}

/** @desc 完成采购订单 */
export function finishPurchaseOrder(data: any) {
  return http.post(`${BASE_URL}/finish`, data)
}

/** @desc 核对采购订单 */
export function checkPurchaseOrder(data: any) {
  return http.get(`${BASE_URL}/check`, data)
}

/** @desc 验收采购订单 */
export function receivePurchaseOrder(data: any) {
  return http.post(`${BASE_URL}/receive`, data)
}
