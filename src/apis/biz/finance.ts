import http from '@/utils/http'

const BASE_URL = '/biz/finance'

export interface CustomerStatReportResp {
  customerId: string
  customerName: string
  balance: number
  transferAmount: number
  adAccountRechargeAmount: number
  fee: number
  withdrawAmount: number
  totalRecharge: number
  adAccountBuyAmount: number
}

export interface CustomerStatSummaryResp {
  transferAmount: number
  refundAmount: number
  adAccountRechargeAmount: number
  fee: number
  withdrawAmount: number
  totalRecharge: number
  adAccountBuyAmount: number
  cardSpent: number
  totalBalance: number
}

export interface CustomerDailyStatReportResp {
  statDate: string
  transferAmount: number
  adAccountRechargeAmount: number
  fee: number
  withdrawAmount: number
  adAccountBuyAmount: number
}

export interface CustomerStatReportQuery {
  customerId: string | undefined
  isSelf: boolean | undefined
  settleType: number | undefined
  businessType: number | undefined
  transTime: Array<string>
  ascSortFlag?: boolean
  sortField?: string
}

export interface CustomerDailyStatReportQuery {
  transTime: Array<string> | undefined
  customerId: string | undefined
}

export interface CustomerStatReportPageQuery extends CustomerStatReportQuery, PageQuery {}

/** @desc 导入用户 */
export function importCustomerDailyReport(id: string, data: any) {
  return http.post(`${BASE_URL}/customer/${id}/report/import`, data)
}

/** @desc 查询客户统计数据列表 */
export function listCustomerStatReport(query: CustomerStatReportPageQuery) {
  return http.get<PageRes<CustomerStatReportResp[]>>(`${BASE_URL}/customer/stat`, query)
}

/** @desc 查询客户统计数据汇总 */
export function getCustomerStatReportSummary(query: CustomerStatReportQuery) {
  return http.get<CustomerStatSummaryResp>(`${BASE_URL}/customer/stat/summary`, query)
}

/** @desc 导出客户统计数据 */
export function exportCustomerStatReport(query: CustomerStatReportQuery) {
  return http.download(`${BASE_URL}/customer/stat/export`, query)
}

/** @desc 查询客户每日统计数据列表 */
export function listCustomerDailyStatReport(id: string, query: CustomerDailyStatReportQuery) {
  return http.get<PageRes<CustomerDailyStatReportResp[]>>(`${BASE_URL}/customer/${id}/daily`, query)
}

/** @desc 导出客户每日统计数据 */
export function exportCustomerDailyStatReport(id: string, query: CustomerDailyStatReportQuery) {
  return http.download(`${BASE_URL}/customer/${id}/daily/export`, query)
}

export interface AdAccountInsightResp {
  id: string
  customerName: string
  adAccountId: string
  spend: number
  recharge: number
  adStatus: number
  timezone: string
  finishTime: string
  recycleTime: string
  cardAmount: number
  diffAmount: number
  browserNo: string
  accountStatus: number
  orderStatus: number
  adAccountFinishTime: string
  adAccountRecycleTime: string
}

export interface AdAccountInsightQuery {
  customerId: string | undefined
  adAccountIds: string[] | undefined // 修改为数组类型
  transTime: Array<string> | undefined
  ascSortFlag?: boolean
  sortField?: string
  accountStatus: string | undefined
}
export interface AdAccountDailyStatReportResp {
  statDate: string
  spend: number
  recharge: number
}
export interface AdAccountInsightPageQuery extends AdAccountInsightQuery, PageQuery {}
/** @desc 查询广告户消耗列表 */
export function listAdAccountInsight(query: AdAccountInsightPageQuery) {
  return http.get<PageRes<AdAccountInsightResp[]>>(`${BASE_URL}/adAccount/stat`, query)
}

export function listAdAccountDailyStatReport(id: string, query: CustomerDailyStatReportQuery) {
  return http.get<PageRes<AdAccountDailyStatReportResp[]>>(`${BASE_URL}/adAccount/${id}/daily`, query)
}
/** @desc 导出广告户消耗 */
export function exportAdAccountInsight(query: AdAccountInsightQuery) {
  return http.download(`${BASE_URL}/adAccount/stat/export`, query)
}

/** @desc 导出广告户每日消耗 */
export function exportAdAccountDailyStatReport(id: string, query: CustomerDailyStatReportQuery) {
  return http.download(`${BASE_URL}/adAccount/${id}/daily/export`, query)
}

export interface AdAccountSalesStatisticsResp {
  date: string
  saleNum: number
  recycleNum: number
  createNum: number
  inventory: number
}
export interface AdAccountSalesStatisticsQuery {
  statTime: undefined | Array<string>
}

/** @desc 查询广告户出售统计列表 */
export function listAdAccountSalesStatistics(query: AdAccountSalesStatisticsQuery) {
  return http.get<PageRes<AdAccountSalesStatisticsResp[]>>(`${BASE_URL}/adAccount/salesStatistics`, query)
}

export function exportAdAccountSalesStatistics(query: AdAccountSalesStatisticsQuery) {
  return http.download(`${BASE_URL}/adAccount/salesStatistics/export`, query)
}

export function getAdAccountSalesStatisticsSummary(query: AdAccountSalesStatisticsQuery) {
  return http.get<AdAccountSalesStatisticsResp>(`${BASE_URL}/adAccount/salesStatistics/summary`, query)
}

export interface DailyStatReportResp {
  statDate: string
  transferAmount: number
  fee: number
  adAccountBuyAmount: number
  cardSpent: number
}

/** @desc 查询每日统计数据列表 */
export function listDailyStatReport(query: CustomerDailyStatReportQuery) {
  return http.get<PageRes<DailyStatReportResp[]>>(`${BASE_URL}/daily`, query)
}

/** @desc 导出每日统计数据 */
export function exportDailyStatReport(query: CustomerDailyStatReportQuery) {
  return http.download(`${BASE_URL}/daily/export`, query)
}

export interface AdAccountRetentionResp {
  date: string
  cost: number
  count: number
  retention1d: number
  retention7d: number
  retention15d: number
  retention30d: number
}

export interface AdAccountRetentionQuery {
  customerId: string | undefined
  bmChannel: string | undefined
  timezone: string | undefined
  finishTime: Array<string> | undefined
}

export interface AdAccountRetentionPageQuery extends AdAccountRetentionQuery, PageQuery {}

/** @desc 查询广告户留存分析列表 */
export function listAdAccountRetention(query: AdAccountRetentionPageQuery) {
  return http.get<PageRes<AdAccountRetentionResp[]>>(`${BASE_URL}/adAccount/retention`, query)
}

/** @desc 导出广告户留存分析 */
export function exportAdAccountRetention(query: AdAccountRetentionQuery) {
  return http.download(`${BASE_URL}/adAccount/retention/export`, query)
}
