import http from '@/utils/http'

const BASE_URL = '/biz/businessManagerStatistics'

export interface BusinessManagerStatisticsResp {
  id: string
  statisticsDate: string
  remainingNormalInventory: string
  dailyReceivedCount: string
  totalCost: string
  usedNormal: string
  unusedNormal: string
  usedBanned: string
  unusedBanned: string
  remainingNormalCount: string
  survivalRate: string
  averagePrepareCost: string
  dailySales: string
  dailyBanned: string
  orderCost: string
  inventoryDetailJson: object
  inventoryDetail: string
}

export interface BusinessManagerStatisticsSummaryResp {
  receiveBMItem: number
  receiveCost: number
  usedNormalBmItem: number
  normalRate: number
  purchaseCost: number
  avgPurchaseCostForDay: number
  avgPrepareCost: number
}

export interface BusinessManagerStatisticsQuery {
  statisticsDate: string | undefined
  sort: Array<string>
}
export interface BusinessManagerStatisticsPageQuery extends BusinessManagerStatisticsQuery, PageQuery {}

/** @desc 查询成本分析列表 */
export function listBusinessManagerStatistics(query: BusinessManagerStatisticsPageQuery) {
  return http.get<PageRes<BusinessManagerStatisticsResp[]>>(`${BASE_URL}`, query)
}

/** @desc 导出成本分析 */
export function exportBusinessManagerStatistics(query: BusinessManagerStatisticsQuery) {
  return http.download(`${BASE_URL}/export`, query)
}

/** @desc 查询成本分析列表 */
export function getBusinessManagerStatisticsSummary(query: BusinessManagerStatisticsQuery) {
  return http.get<BusinessManagerStatisticsSummaryResp>(`${BASE_URL}/summary`, query)
}

export interface SpendStatisticsReq {
  completeTimeStart: string
  completeTimeEnd: string
  statisticsTimeStart: string
  statisticsTimeEnd: string
}

export interface SpendStatisticsSummaryResp {
  totalOrderCount: number
  totalNormalCount: number
  totalOrderCost: number
  saleOrderCount: number
  recycleOrderCount: number
}

export interface DailySpendResp {
  tradeTime: string
  totalSpend: number
}

/** @desc 获取消耗统计汇总数据 */
export function getSpendSummary(query: SpendStatisticsReq) {
  return http.get<SpendStatisticsSummaryResp>(`${BASE_URL}/spend/summary`, query)
}

/** @desc 获取每日消耗记录 */
export function getDailySpend(query: SpendStatisticsReq) {
  return http.get<DailySpendResp[]>(`${BASE_URL}/spend/daily`, query)
}

export interface BusinessManagerChannelStatQuery {
  channelId?: number
  statisticsDate?: string[]
  page?: number
  size?: number
}

export interface BusinessManagerChannelStatDataResp {
  channelName: string
  totalCount: number
  availableCount: number
  bannedCount: number
  lowerLimitCount: number
  usedBannedCount: number
  lowerLimitAdCount: number
  statDate: string
}

/** @desc 分页查询BM渠道分析 */
export function listBusinessManagerChannelStat(query: BusinessManagerChannelStatQuery) {
  return http.get<PageRes<BusinessManagerChannelStatDataResp[]>>(`${BASE_URL}/channelStat`, query)
}

export interface BusinessManagerBanStatQuery {
  channelId?: number
  statisticsDate?: string[]
  page?: number
  size?: number
}

export interface BusinessManagerBanStatDataResp {
  channelName: string
  bannedReason: string
  bannedCount: number
}

/** @desc 分页查询BM封禁分析 */
export function listBusinessManagerBanStat(query: BusinessManagerBanStatQuery) {
  return http.get<PageRes<BusinessManagerBanStatDataResp[]>>(`${BASE_URL}/banStat`, query)
}

export function syncBusinessManagerStatistics(id: string) {
  return http.put(`${BASE_URL}/${id}/sync`)
}
