import http from '@/utils/http'

const BASE_URL = '/biz/profitType'

export interface ProfitTypeResp {
  id: string
  adPlatform: string
  project: string
  name: string
  createUser: string
  createTime: string
  createUserString: string
  updateUserString: string
  disabled: boolean
}
export interface ProfitTypeDetailResp {
  id: string
  adPlatform: string
  project: string
  name: string
  createUser: string
  createTime: string
  createUserString: string
  updateUserString: string
}
export interface ProfitTypeQuery {
  adPlatform: string | undefined
  project: string | undefined
  name: string | undefined
  sort: Array<string>
}
export interface ProfitTypePageQuery extends ProfitTypeQuery, PageQuery {}

/** @desc 查询利润类型列表 */
export function listProfitType(query: ProfitTypePageQuery) {
  return http.get<PageRes<ProfitTypeResp[]>>(`${BASE_URL}`, query)
}

/** @desc 查询利润类型详情 */
export function getProfitType(id: string) {
  return http.get<ProfitTypeDetailResp>(`${BASE_URL}/${id}`)
}

/** @desc 新增利润类型 */
export function addProfitType(data: any) {
  return http.post(`${BASE_URL}`, data)
}

/** @desc 修改利润类型 */
export function updateProfitType(data: any, id: string) {
  return http.put(`${BASE_URL}/${id}`, data)
}

/** @desc 删除利润类型 */
export function deleteProfitType(id: string) {
  return http.del(`${BASE_URL}/${id}`)
}

/** @desc 导出利润类型 */
export function exportProfitType(query: ProfitTypeQuery) {
  return http.download(`${BASE_URL}/export`, query)
}
