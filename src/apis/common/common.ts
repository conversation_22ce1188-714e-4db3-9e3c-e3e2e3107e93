import type { TreeNodeData } from '@arco-design/web-vue'
import http from '@/utils/http'
import type { LabelValueState } from '@/types/global'
import type { BusinessManagerQuery } from '@/apis/biz/businessManager'
import type { CustomerQuery } from '@/apis/biz/customer'
import type { UserQuery } from '@/apis'
import type { AgentQuery } from '@/apis/biz/agent'
import type { AdProductQuery } from '@/apis/biz/adProduct'
import type { ProfitTypeQuery } from '@/apis/biz/profitType'
import type { TransactionUserQuery } from '@/apis/biz/transactionUser'

const BASE_URL = '/common'

/** @desc 查询部门树 */
export function listDeptTree(query: { description: string }) {
  return http.get<TreeNodeData[]>(`${BASE_URL}/tree/dept`, query)
}

/** @desc 查询菜单树 */
export function listMenuTree(query: { description: string }) {
  return http.get<TreeNodeData[]>(`${BASE_URL}/tree/menu`, query)
}

/** @desc 查询用户列表 */
export function listUserDict(query?: UserQuery) {
  return http.get<LabelValueState[]>(`${BASE_URL}/dict/all_user`, query)
}

/** @desc 查询所有用户列表 */
export function listAllUserDict(query?: { status?: number, roleCodePrefix?: string }) {
  return http.get<LabelValueState[]>(`${BASE_URL}/dict/all_user`, query)
}

/** @desc 查询来源列表 */
export function listSourceDict(query?: { status: number }) {
  return http.get<LabelValueState[]>(`${BASE_URL}/dict/source`, query)
}

/** @desc 查询角色列表 */
export function listRoleDict(query?: { name: string, status: number }) {
  return http.get<LabelValueState[]>(`${BASE_URL}/dict/role`, query)
}

/** @desc 查询字典列表 */
export function listCommonDict(code: string) {
  return http.get<LabelValueState[]>(`${BASE_URL}/dict/${code}`)
}

/** @desc 查询系统配置参数 */
export function listSiteOptionDict() {
  return http.get<LabelValueState[]>(`${BASE_URL}/dict/option/site`)
}

/** @desc 上传文件 */
export function uploadFile(data: FormData) {
  return http.post(`${BASE_URL}/file`, data)
}

/** @desc 查询客户列表 */
export function listCustomerDict(query?: CustomerQuery) {
  return http.get<LabelValueState[]>(`${BASE_URL}/dict/customer`, query)
}

/** @desc 查询bm5列表 */
export function listBusinessManagerDict(query?: BusinessManagerQuery) {
  return http.get<LabelValueState[]>(`${BASE_URL}/dict/businessManager`, query)
}

/** @desc 查询bm5渠道列表 */
export function listBusinessManagerChannelDict() {
  return http.get<LabelValueState[]>(`${BASE_URL}/dict/businessManagerChannel`)
}

/** @desc 查询个号渠道列表 */
export function listFbChannelDict() {
  return http.get<LabelValueState[]>(`${BASE_URL}/dict/fbChannel`)
}

/** @desc 查询bm5列表 */
export function getCardPlatformBalance(platform: string) {
  return http.get<number>(`${BASE_URL}/cardPlatform/balance`, { platform })
}

/** @desc 查询标签列表 */
export function listTagDict() {
  return http.get<LabelValueState[]>(`${BASE_URL}/dict/tag`)
}

/** @desc 查询社交账户字典 */
export function listSourceAccount(query: any) {
  return http.get<LabelValueState[]>(`${BASE_URL}/dict/socialAccount`, query)
}

/** @desc 查询中介列表 */
export function listAgentDict(query?: AgentQuery) {
  return http.get<LabelValueState[]>(`${BASE_URL}/dict/agent`, query)
}

/** @desc 查询投放产品列表 */
export function listAdProductDict(query?: AdProductQuery) {
  return http.get<LabelValueState[]>(`${BASE_URL}/dict/adProduct`, query)
}

/** @desc 查询利润类型列表 */
export function listProfitTypeDict(query?: ProfitTypeQuery) {
  return http.get<LabelValueState[]>(`${BASE_URL}/dict/profitType`, query)
}

/** @desc 查询交易客户列表 */
export function listTransactionUserDict(query?: TransactionUserQuery) {
  return http.get<LabelValueState[]>(`${BASE_URL}/dict/transactionUser`, query)
}
