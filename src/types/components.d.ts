/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AdAccountCard: typeof import('./../components/AdAccountCard/index.vue')['default']
    AdAccountTypeSelect: typeof import('./../components/AdAccountTypeSelect/index.vue')['default']
    AdsBrowser: typeof import('./../components/AdsBrowser/index.vue')['default']
    AgentSelect: typeof import('./../components/AgentSelect/index.vue')['default']
    Avatar: typeof import('./../components/Avatar/index.vue')['default']
    Bm1Select: typeof import('./../components/Bm1Select/index.vue')['default']
    Breadcrumb: typeof import('./../components/Breadcrumb/index.vue')['default']
    BusinessUserSelect: typeof import('./../components/BusinessUserSelect/index.vue')['default']
    CellCopy: typeof import('./../components/CellCopy/index.vue')['default']
    Chart: typeof import('./../components/Chart/index.vue')['default']
    CNYToUSDInput: typeof import('./../components/CNYToUSDInput/index.vue')['default']
    CronForm: typeof import('./../components/GenCron/CronForm/index.vue')['default']
    CronModal: typeof import('./../components/GenCron/CronModal/index.vue')['default']
    CustomerSelect: typeof import('./../components/CustomerSelect/index.vue')['default']
    DateRangePicker: typeof import('./../components/DateRangePicker/index.vue')['default']
    DayForm: typeof import('./../components/GenCron/CronForm/component/day-form.vue')['default']
    ExcelExport: typeof import('./../components/ExcelExport/index.vue')['default']
    FilePreview: typeof import('./../components/FilePreview/index.vue')['default']
    FUserSelect: typeof import('./../components/FUserSelect/index.vue')['default']
    GiCellAvatar: typeof import('./../components/GiCell/GiCellAvatar.vue')['default']
    GiCellGender: typeof import('./../components/GiCell/GiCellGender.vue')['default']
    GiCellStatus: typeof import('./../components/GiCell/GiCellStatus.vue')['default']
    GiCellTag: typeof import('./../components/GiCell/GiCellTag.vue')['default']
    GiCellTags: typeof import('./../components/GiCell/GiCellTags.vue')['default']
    GiCodeView: typeof import('./../components/GiCodeView/index.vue')['default']
    GiDot: typeof import('./../components/GiDot/index.tsx')['default']
    GiEditTable: typeof import('./../components/GiEditTable/GiEditTable.vue')['default']
    GiFlexibleBox: typeof import('./../components/GiFlexibleBox/index.vue')['default']
    GiFooter: typeof import('./../components/GiFooter/index.vue')['default']
    GiForm: typeof import('./../components/GiForm/src/GiForm.vue')['default']
    GiIconSelector: typeof import('./../components/GiIconSelector/index.vue')['default']
    GiIframe: typeof import('./../components/GiIframe/index.vue')['default']
    GiOption: typeof import('./../components/GiOption/index.vue')['default']
    GiOptionItem: typeof import('./../components/GiOptionItem/index.vue')['default']
    GiOverFlowTags: typeof import('./../components/GiOverFlowTags/index.vue')['default']
    GiSvgIcon: typeof import('./../components/GiSvgIcon/index.vue')['default']
    GiTable: typeof import('./../components/GiTable/index.vue')['default']
    GiTag: typeof import('./../components/GiTag/index.tsx')['default']
    GiThemeBtn: typeof import('./../components/GiThemeBtn/index.vue')['default']
    HourForm: typeof import('./../components/GenCron/CronForm/component/hour-form.vue')['default']
    Icon403: typeof import('./../components/icons/Icon403.vue')['default']
    Icon404: typeof import('./../components/icons/Icon404.vue')['default']
    Icon500: typeof import('./../components/icons/Icon500.vue')['default']
    IconBorders: typeof import('./../components/icons/IconBorders.vue')['default']
    IconTableSize: typeof import('./../components/icons/IconTableSize.vue')['default']
    IconTreeAdd: typeof import('./../components/icons/IconTreeAdd.vue')['default']
    IconTreeReduce: typeof import('./../components/icons/IconTreeReduce.vue')['default']
    JsonPretty: typeof import('./../components/JsonPretty/index.vue')['default']
    MinuteForm: typeof import('./../components/GenCron/CronForm/component/minute-form.vue')['default']
    MonthForm: typeof import('./../components/GenCron/CronForm/component/month-form.vue')['default']
    MonthRangePicker: typeof import('./../components/MonthRangePicker/index.vue')['default']
    OperationUserSelect: typeof import('./../components/OperationUserSelect/index.vue')['default']
    ParentView: typeof import('./../components/ParentView/index.vue')['default']
    PasteImage: typeof import('./../components/PasteImage/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SecondForm: typeof import('./../components/GenCron/CronForm/component/second-form.vue')['default']
    Tag: typeof import('./../components/Tag/index.vue')['default']
    TextCopy: typeof import('./../components/TextCopy/index.vue')['default']
    TrueAndFalseSelect: typeof import('./../components/TrueAndFalseSelect/index.vue')['default']
    UserSelect: typeof import('./../components/UserSelect/index.vue')['default']
    Verify: typeof import('./../components/Verify/index.vue')['default']
    VerifyPoints: typeof import('./../components/Verify/Verify/VerifyPoints.vue')['default']
    VerifySlide: typeof import('./../components/Verify/Verify/VerifySlide.vue')['default']
    WeekForm: typeof import('./../components/GenCron/CronForm/component/week-form.vue')['default']
    YearForm: typeof import('./../components/GenCron/CronForm/component/year-form.vue')['default']
  }
}
