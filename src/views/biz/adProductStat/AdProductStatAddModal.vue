<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 600 ? 600 : '100%'"
    draggable
    @before-ok="save"
    @close="reset"
  >
    <GiForm ref="formRef" v-model="form" :options="options" :columns="columns" />
    <a-form :model="extraDataForm" :auto-label-width="true" style="margin-top: 20px;">
      <a-form-item v-for="(item, index) in extraDataForm.data" :key="index" :field="`data.${index}.key`" :label="item.key" required>
        <a-input v-model="item.value" placeholder="请输入" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import { useWindowSize } from '@vueuse/core'
import { addAdProductStat, getAdProductStat, updateAdProductStat } from '@/apis/biz/adProductStat'
import { type Columns, GiForm, type Options } from '@/components/GiForm'
import { useResetReactive } from '@/hooks'
import { useCustomer } from '@/hooks/app'
import type { LabelValueState } from '@/types/global'
import { listAdProductDict } from '@/apis'
import { getCustomer } from '@/apis/biz/customer'

const emit = defineEmits<{
  (e: 'save-success'): void
}>()

const { width } = useWindowSize()

const dataId = ref('')
const visible = ref(false)
const isUpdate = computed(() => !!dataId.value)
const title = computed(() => (isUpdate.value ? '修改代理线日报' : '新增代理线日报'))
const formRef = ref<InstanceType<typeof GiForm>>()

const { customerList, getCustomerList } = useCustomer()

const adProductList = ref<LabelValueState[]>([])

const options: Options = {
  form: { size: 'large' },
  btns: { hide: true },
}

const [extraDataForm, resetExtraDataForm] = useResetReactive({
  // todo 待补充
  data: [],
})

const [form, resetForm] = useResetReactive({
  // todo 待补充
  reflowSpend: 0,
})

const getAdProductList = async () => {
  const { data } = await listAdProductDict({ customerId: form.customerId })
  adProductList.value = data
}

const onCustomerChange = async () => {
  if (form.customerId) {
    resetExtraDataForm()
    await getAdProductList()
    const { data } = await getCustomer(form.customerId)
    extraDataForm.data = JSON.parse(data.dataTemplate)
  } else {
    resetExtraDataForm()
    adProductList.value = []
  }
  form.productId = undefined
  await getAdProductList()
}

const columns: Columns = reactive<Columns>([
  {
    label: '统计日期',
    field: 'statDate',
    type: 'date-picker',
    disabled: () => isUpdate.value,
    rules: [{ required: true, message: '请选择统计日期' }],
    props: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '客户',
    field: 'customerId',
    type: 'select',
    options: customerList,
    disabled: () => isUpdate.value,
    rules: [{ required: true, message: '请输入客户' }],
    props: {
      allowSearch: true,
      allowClear: false,
      onChange: onCustomerChange,
    },
  },
  {
    label: '代理线',
    field: 'productId',
    type: 'select',
    options: adProductList,
    disabled: () => isUpdate.value,
    rules: [{ required: true, message: '请输入产品' }],
    props: {
      allowSearch: true,
    },
  },
  {
    label: '消耗',
    field: 'spend',
    type: 'input-number',
    rules: [{ required: true, message: '请输入消耗' }],
    props: {
      min: 0,
    },
  },
  {
    label: '回流',
    field: 'reflowSpend',
    type: 'input-number',
    rules: [{ required: true, message: '请输入回流' }],
    props: {
      min: 0,
    },
  },
  {
    label: '承担费用',
    field: 'bearCost',
    type: 'input-number',
    props: {
      min: 0,
    },
  },
])

// 重置
const reset = () => {
  formRef.value?.formRef?.resetFields()
  resetForm()
  resetExtraDataForm()
}

// 保存
const save = async () => {
  try {
    const isInvalid = await formRef.value?.formRef?.validate()
    if (isInvalid) return false
    form.extraData = JSON.stringify(extraDataForm.data)
    if (isUpdate.value) {
      await updateAdProductStat(form, dataId.value)
      Message.success('修改成功')
    } else {
      await addAdProductStat(form)
      Message.success('新增成功')
    }
    emit('save-success')
    return true
  } catch (error) {
    return false
  }
}

// 新增
const onAdd = async () => {
  reset()
  dataId.value = ''
  await getCustomerList({ isSelfAccount: false, status: 1, type: 1, businessType: 2 })
  visible.value = true
}

// 修改
const onUpdate = async (id: string) => {
  reset()
  dataId.value = id
  const { data } = await getAdProductStat(id)
  Object.assign(form, data)
  if (data.extraData) {
    extraDataForm.data = JSON.parse(data.extraData)
  }
  await getCustomerList({ isSelfAccount: false, status: 1, type: 1, businessType: 2 })
  await getAdProductList()
  visible.value = true
}

defineExpose({ onAdd, onUpdate })
</script>

<style scoped lang="scss"></style>
