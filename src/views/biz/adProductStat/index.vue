<template>
  <div class="table-page">
    <GiTable
      title="代理线日报管理"
      row-key="id"
      :data="dataList"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
      :pagination="pagination"
      :disabled-tools="['size']"
      :disabled-column-keys="['name']"
      @refresh="search"
    >
      <template #toolbar-left>
        <a-select
          v-model="queryForm.customerId"
          :options="customerList"
          placeholder="请选择客户"
          allow-clear
          style="width: 150px"
          @change="getAdProductList"
        />
        <a-select
          v-model="queryForm.productId"
          :options="adProductList"
          placeholder="请选择产品"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <DateRangePicker v-model="queryForm.statDate" format="YYYY-MM-DD" :show-time="false" @change="search" />
        <a-input-search v-model="queryForm.platformAdId" placeholder="请输入广告户" allow-clear @search="search" />
        <a-button @click="reset">
          <template #icon><icon-refresh /></template>
          <template #default>重置</template>
        </a-button>
      </template>
      <template #toolbar-right>
        <a-button v-permission="['biz:adProductStat:add']" type="primary" @click="onAdd">
          <template #icon><icon-plus /></template>
          <template #default>新增</template>
        </a-button>
        <a-button v-permission="['biz:adProductStat:export']" @click="onExport">
          <template #icon><icon-download /></template>
          <template #default>导出</template>
        </a-button>
      </template>
      <template #action="{ record }">
        <a-space>
          <a-link v-permission="['biz:adProductStat:update']" title="修改" @click="onItemListShow(record)">消耗</a-link>
          <a-link v-permission="['biz:adProductStat:update']" title="修改" @click="onUpdate(record)">修改</a-link>
          <a-link
            v-permission="['biz:adProductStat:delete']"
            status="danger"
            :disabled="record.disabled"
            :title="record.disabled ? '不可删除' : '删除'"
            @click="onDelete(record)"
          >
            删除
          </a-link>
        </a-space>
      </template>
    </GiTable>

    <AdProductStatAddModal ref="AdProductStatAddModalRef" @save-success="search" />
    <AdProductStatItemListModal ref="AdProductStatItemListModalRef" @save-success="search"></AdProductStatItemListModal>
  </div>
</template>

<script setup lang="ts">
import AdProductStatAddModal from './AdProductStatAddModal.vue'
import { type AdProductStatQuery, type AdProductStatResp, deleteAdProductStat, exportAdProductStat, listAdProductStat } from '@/apis/biz/adProductStat'
import type { TableInstanceColumns } from '@/components/GiTable/type'
import { useDownload, useTable } from '@/hooks'
import { isMobile } from '@/utils'
import has from '@/utils/has'
import { useCustomer } from '@/hooks/app'
import { listAdProductDict } from '@/apis'
import type { LabelValueState } from '@/types/global'
import AdProductStatItemListModal from '@/views/biz/adProductStat/item/AdProductStatItemListModal.vue'

defineOptions({ name: 'AdProductStat' })

const { customerList, getCustomerList } = useCustomer()

const queryForm = reactive<AdProductStatQuery>({
  customerId: undefined,
  productId: undefined,
  statDate: undefined,
  platformAdId: undefined,
  sort: ['statDate,desc'],
})

const adProductList = ref<LabelValueState[]>([])

const {
  tableData: dataList,
  loading,
  pagination,
  search,
  handleDelete,
} = useTable((page) => listAdProductStat({ ...queryForm, ...page }), { immediate: true })
const columns = ref<TableInstanceColumns[]>([
  {
    title: '序号',
    width: 66,
    align: 'center',
    render: ({ rowIndex }) => h('span', {}, rowIndex + 1 + (pagination.current - 1) * pagination.pageSize),
    fixed: !isMobile() ? 'left' : undefined,
  },
  { title: '统计日期', dataIndex: 'statDate', slotName: 'statDate', align: 'center', width: 140 },
  { title: '客户', dataIndex: 'customerName', slotName: 'customerName', width: 160 },
  { title: '代理线', dataIndex: 'productName', slotName: 'productName', width: 160 },
  { title: 'FB消耗', dataIndex: 'actualSpend', slotName: 'actualSpend', align: 'center', width: 100 },
  { title: '日消耗', dataIndex: 'spend', slotName: 'spend', align: 'center', width: 100 },
  { title: '回流', dataIndex: 'reflowSpend', slotName: 'reflowSpend', align: 'center', width: 100 },
  { title: '费率(%)', dataIndex: 'feeRate', slotName: 'feeRate', align: 'center', width: 100 },
  { title: '服务费', dataIndex: 'fee', slotName: 'fee', align: 'center', width: 100 },
  { title: '承担费用', dataIndex: 'bearCost', slotName: 'bearCost', align: 'center', width: 100 },
  { title: '总消耗', dataIndex: 'totalCost', slotName: 'totalCost', align: 'center', width: 100 },
  { title: '创建时间', dataIndex: 'createTime', slotName: 'createTime', width: 180 },
  {
    title: '操作',
    dataIndex: 'action',
    slotName: 'action',
    width: 160,
    align: 'center',
    fixed: !isMobile() ? 'right' : undefined,
    show: has.hasPermOr(['biz:adProductStat:detail', 'biz:adProductStat:update', 'biz:adProductStat:delete']),
  },
])

// 重置
const reset = () => {
  queryForm.customerId = undefined
  queryForm.productId = undefined
  queryForm.statDate = undefined
  queryForm.platformAdId = undefined
  search()
}

// 删除
const onDelete = (record: AdProductStatResp) => {
  return handleDelete(() => deleteAdProductStat(record.id), {
    content: `是否确定删除该条数据？`,
    showModal: true,
  })
}

// 导出
const onExport = () => {
  useDownload(() => exportAdProductStat(queryForm))
}

const AdProductStatAddModalRef = ref<InstanceType<typeof AdProductStatAddModal>>()
// 新增
const onAdd = () => {
  AdProductStatAddModalRef.value?.onAdd()
}

// 修改
const onUpdate = (record: AdProductStatResp) => {
  AdProductStatAddModalRef.value?.onUpdate(record.id)
}

const getAdProductList = async () => {
  const { data } = await listAdProductDict({ customerId: queryForm.customerId })
  adProductList.value = data
  search()
}

const AdProductStatItemListModalRef = ref<InstanceType<typeof AdProductStatItemListModal>>()
// 新增
const onItemListShow = (record: AdProductStatResp) => {
  AdProductStatItemListModalRef.value?.onOpen(record.id, record.customerId)
}

getCustomerList({ isSelfAccount: false, status: 1, type: 1, businessType: 2 })
</script>

<style scoped lang="scss"></style>
