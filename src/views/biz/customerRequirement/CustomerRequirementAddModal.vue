<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 600 ? 600 : '100%'"
    draggable
    @before-ok="save"
    @close="reset"
  >
    <GiForm ref="formRef" v-model="form" :options="options" :columns="columns" />
  </a-modal>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import { useWindowSize } from '@vueuse/core'
import dayjs from 'dayjs'
import { addCustomerRequirement, getCustomerRequirement, updateCustomerRequirement } from '@/apis/biz/customerRequirement'
import { type Columns, GiForm, type Options } from '@/components/GiForm'
import { useResetReactive } from '@/hooks'
import { useCustomer, useDict } from '@/hooks/app'
import type { LabelValueState } from '@/types/global'
import { listProfitTypeDict } from '@/apis'

const emit = defineEmits<{
  (e: 'save-success'): void
}>()

const { width } = useWindowSize()

const dataId = ref('')
const visible = ref(false)
const isUpdate = computed(() => !!dataId.value)
const title = computed(() => (isUpdate.value ? '修改客户需求' : '新增客户需求'))
const formRef = ref<InstanceType<typeof GiForm>>()

const options: Options = {
  form: { size: 'large' },
  btns: { hide: true },
}

const [form, resetForm] = useResetReactive({
  customerId: undefined,
  timezone: undefined,
  requirementTime: undefined,
  quantity: undefined,
  remark: '',
  bmType: undefined,
  payAmount: undefined,
  adAccountName: '',
  customerBmId: '',
  notHandle: false, // 添加是否暂不处理字段
  status: 1, // 添加状态字段
})

const business_manager_type = ref([{
  label: '大小黑',
  value: -1,
}])

const { timezone_type } = useDict('timezone_type')

const { customerList, getCustomerList } = useCustomer()

const profitTypeList = ref<LabelValueState[]>([])

const getProfitTypeList = async () => {
  const { data } = await listProfitTypeDict({ adPlatform: '1', cat: 'ad_account' })
  profitTypeList.value = data
}

// Initialize customer list
onMounted(() => {
  getCustomerList()
})

// 监听 notHandle 变化，更新 status
watch(() => form.notHandle, (newVal) => {
  form.status = newVal ? 0 : 1
})

const columns: Columns = reactive<Columns>([
  {
    label: '客户',
    field: 'customerId',
    type: 'select',
    options: customerList,
    rules: [{ required: true, message: '请选择客户' }],
    props: {
      allowSearch: true,
    },
  },
  {
    label: '时区',
    field: 'timezone',
    type: 'select',
    options: timezone_type,
    rules: [{ required: true, message: '请输入时区' }],
  },
  {
    label: '需求时间',
    field: 'requirementTime',
    type: 'date-picker',
    props: {
      showTime: false,
      disabledDate: (current) => {
        return dayjs(current).isBefore(dayjs().subtract(1, 'days'))
      },
    },
    rules: [{ required: true, message: '请输入需求时间' }],
  },
  {
    label: '广告户类型',
    field: 'bmType',
    type: 'select',
    options: profitTypeList,
    rules: [{ required: true, message: '请选择BM类型' }],
  },
  {
    label: '需求数量',
    field: 'quantity',
    type: 'input-number',
    props: {
      min: 1,
      precision: 0,
      placeholder: '请输入需求数量',
    },
    rules: [{ required: true, message: '请输入需求数量' }],
  },
  {
    label: '开户费',
    field: 'payAmount',
    type: 'input-number',
    props: {
      min: 0,
      precision: 2,
      placeholder: '请输入开户费',
    },
  },
  {
    label: '客户BM ID',
    field: 'customerBmId',
    type: 'input',
  },
  {
    label: '客户邮箱',
    field: 'customerEmail',
    type: 'input',
  },
  {
    label: '广告户名字',
    field: 'adAccountName',
    type: 'textarea',
    formItemProps: {
      help: '名字和需求数量一样用,隔开',
    },
    props: {
      autoSize: { minRows: 2, maxRows: 6 },
    },
  },
  {
    label: '备注',
    field: 'remark',
    type: 'input',
  },
  {
    label: '是否暂不处理',
    field: 'notHandle',
    type: 'radio-group',
    options: [
      { label: '否', value: false },
      { label: '是', value: true },
    ],
  },
])

// 重置
const reset = () => {
  formRef.value?.formRef?.resetFields()
  resetForm()
}

// 保存
const save = async () => {
  try {
    const isInvalid = await formRef.value?.formRef?.validate()
    if (isInvalid) return false
    if (isUpdate.value) {
      await updateCustomerRequirement(form, dataId.value)
      Message.success('修改成功')
    } else {
      await addCustomerRequirement(form)
      Message.success('新增成功')
    }
    emit('save-success')
    return true
  } catch (error) {
    return false
  }
}

// 新增
const onAdd = async () => {
  reset()
  dataId.value = ''
  await getProfitTypeList()
  visible.value = true
}

// 修改
const onUpdate = async (id: string) => {
  reset()
  dataId.value = id
  const { data } = await getCustomerRequirement(id)
  Object.assign(form, data)
  form.notHandle = data.status === 0
  await getProfitTypeList()
  visible.value = true
}

defineExpose({ onAdd, onUpdate })
</script>

<style scoped lang="scss"></style>
