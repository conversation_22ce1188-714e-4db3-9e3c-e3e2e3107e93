<template>
  <div class="table-page">
    <!-- 添加汇总统计 -->
    <div class="statistic-container">
      <a-statistic
        v-for="(item, index) in summaryData"
        :key="index"
        :title="item.timezone"
        :value="item.count"
      />
    </div>

    <GiTable
      v-model:selectedKeys="selectedKeys"
      title="客户需求管理"
      row-key="id"
      :data="dataList"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
      :pagination="pagination"
      :disabled-tools="['size']"
      :disabled-column-keys="['name']"
      :row-selection="{
        type: 'checkbox',
        showCheckedAll: true,
        onlyCurrent: true,
      }"
      @refresh="search"
    >
      <template #toolbar-bottom>
        <a-descriptions :column="2">
          <a-descriptions-item label="需求数量">{{ requiredNum }}</a-descriptions-item>
        </a-descriptions>
      </template>
      <template #toolbar-left>
        <!-- 修改客户筛选 -->
        <a-select
          v-model="queryForm.customerId"
          :options="customerList"
          placeholder="请选择客户"
          allow-clear
          allow-search
          style="width: 150px"
          @change="search"
        />
        <!-- 修改时区筛选 -->
        <a-select
          v-model="queryForm.timezone"
          :options="timezone_type"
          placeholder="请选择时区"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <a-select
          v-model="queryForm.status"
          :options="customer_requirement_status"
          placeholder="请选择状态"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <a-select
          v-model="queryForm.handleUser"
          placeholder="请选择处理人"
          :options="userList"
          allow-clear
          allow-search
          style="width: 150px"
          @change="search"
        />
        <DateRangePicker
          v-model="queryForm.requirementTime" :show-time="false"
          :placeholder="['需求时间', '需求时间']" @change="search"
        />
        <DateRangePicker v-model="queryForm.createTime" :show-time="false" :placeholder="['创建时间', '创建时间']" @change="search" />
        <a-input-search v-model="queryForm.remark" placeholder="请输入备注" allow-clear @search="search" />

        <a-button @click="reset">
          <template #icon><icon-refresh /></template>
          <template #default>重置</template>
        </a-button>
      </template>
      <template #status="{ record }">
        <GiCellTag :value="record.status" :dict="customer_requirement_status" />
      </template>
      <template #toolbar-right>
        <a-button v-permission="['biz:customerRequirement:add']" type="primary" @click="onAdd">
          <template #icon><icon-plus /></template>
          <template #default>新增</template>
        </a-button>
        <a-button
          v-permission="['biz:customerRequirement:add']"
          type="primary"
          :disabled="!selectedKeys.length"
          @click="onBatchHandle"
        >
          <template #icon><icon-check /></template>
          <template #default>运营处理</template>
        </a-button>
      </template>

      <template #action="{ record }">
        <a-space>
          <a-link v-if="record.status <= 2" v-permission="['biz:customerRequirement:update']" title="修改" @click="onUpdate(record)">修改</a-link>

          <a-link v-if="record.status === 2" v-permission="['biz:customerRequirement:update']" title="已邀请" @click="onInvited(record)">已邀请</a-link>

          <a-link v-if="record.status === 0" v-permission="['biz:customerRequirement:update']" title="待处理" @click="onWait(record)">待处理</a-link>

          <a-link v-if="record.status === 4" v-permission="['biz:customerRequirement:update']" title="完成" @click="onFinish(record)">完成</a-link>

          <a-link v-if="record.status === 5" v-permission="['biz:customerRequirement:confirm']" @click="onUpdate(record)">确认需求</a-link>

          <a-link v-if="record.status === 2 || record.status === 5" v-permission="['biz:customerRequirement:update']" title="取消" status="danger" @click="onCancel(record)">取消</a-link>

          <a-link
            v-if="record.status === 1 || record.status === 0"
            v-permission="['biz:customerRequirement:delete']"
            status="danger"
            @click="onDelete(record)"
          >
            删除
          </a-link>
          <!-- 添加下单按钮 -->
          <a-link
            v-if="record.status !== 0 && record.status !== 5"
            title="下单" @click="onCreateOrder(record)"
          >
            下单
          </a-link>
        </a-space>
      </template>
      <template #orderNos="{ record }">
        <div v-if="record.orderNos" style="white-space: pre-line">
          {{ record.orderNos.join('\n') }}
        </div>
      </template>
    </GiTable>

    <a-modal
      v-model:visible="finishVisible"
      title="完成客户需求"
      @ok="handleFinish"
      @cancel="handleFinishCancel"
    >
      <a-form :model="finishForm">
        <a-form-item
          label="完成数量"
          required
        >
          <a-input-number
            v-model="finishForm.quantity"
            placeholder="请输入完成数量"
            :min="1"
            :precision="0"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <a-modal
      v-model:visible="cancelVisible"
      title="取消客户需求"
      @ok="handleCancel"
      @cancel="handleCancelCancel"
    >
      <a-form :model="cancelForm">
        <a-form-item label="取消原因">
          <a-textarea
            v-model="cancelForm.reason"
            placeholder="请输入取消原因"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <CustomerRequirementAddModal ref="CustomerRequirementAddModalRef" @save-success="search" />
    <CustomerRequirementOrderModal ref="orderModalRef" @save-success="search" />
  </div>
</template>

<script setup lang="ts">
import { Message, Modal } from '@arco-design/web-vue'
import CustomerRequirementOrderModal from './CustomerRequirementOrderModal.vue'
import CustomerRequirementAddModal from './CustomerRequirementAddModal.vue'
import {
  type CustomerRequirementQuery,
  type CustomerRequirementResp,
  acceptCustomerRequirements,
  cancelCustomerRequirement,
  deleteCustomerRequirement,
  finishCustomerRequirement,
  getRequiredNum,
  getRequirementSummary,
  invitedCustomerRequirement,
  listCustomerRequirement,
  waitCustomerRequirement,
} from '@/apis/biz/customerRequirement'
import type { TableInstanceColumns } from '@/components/GiTable/type'
import { useTable } from '@/hooks'
import { useCustomer, useDict } from '@/hooks/app'
import { listUserDict } from '@/apis'

import { isMobile } from '@/utils'
import has from '@/utils/has'
import type { LabelValueState } from '@/types/global'

defineOptions({ name: 'CustomerRequirement' })
const requiredNum = ref(0)
const { timezone_type, customer_requirement_status } = useDict(
  'timezone_type',
  'customer_requirement_status',
)
const { customerList, getCustomerList } = useCustomer()
const selectedKeys = ref<string[]>([])
const orderModalRef = ref()

// 添加用户列表状态
const userList = ref<LabelValueState[]>([])

// 添加获取用户列表方法
const getUserList = async () => {
  const { data } = await listUserDict()
  userList.value = data
}

// 添加汇总数据
const summaryData = ref([])

// 获取汇总数据
const getSummaryData = async () => {
  const { data } = await getRequirementSummary(queryForm)
  summaryData.value = data
}

const queryForm = reactive<CustomerRequirementQuery>({
  customerId: undefined,
  timezone: undefined,
  requirementTime: undefined,
  createTime: undefined,
  remark: undefined,
  createUser: undefined,
  sort: ['id,desc'],
  handleUser: undefined,
  handled: undefined,
  status: undefined,
})

const {
  tableData: dataList,
  loading,
  pagination,
  search: originalSearch,
  handleDelete,
} = useTable((page) => listCustomerRequirement({ ...queryForm, ...page }), { immediate: false })

const updateRequiredNum = async (record: CustomerRequirementQuery) => {
  const res = await getRequiredNum(record)
  requiredNum.value = res.data
}

// 重写 search 方法
const search = async () => {
  await originalSearch()
  await getSummaryData()
  await updateRequiredNum(queryForm)
}

// 批量处理
const onBatchHandle = () => {
  const hasHandled = dataList.value.some((item) =>
    selectedKeys.value.includes(item.id) && item.status !== 1,
  )

  if (hasHandled) {
    Message.error('选中记录中包含无需处理或已处理的数据，请重新选择')
    return
  }

  Modal.confirm({
    title: '确认处理',
    content: '是否确认处理选中的记录？',
    onOk: () => {
      return acceptCustomerRequirements(selectedKeys.value).then(() => {
        Message.success('处理成功')
        selectedKeys.value = []
        search()
      })
    },
  })
}
const columns = ref<TableInstanceColumns[]>([
  { title: '客户', dataIndex: 'customerName', width: 120 },
  { title: '客户余额', dataIndex: 'customerBalance', width: 120 },
  { title: '时区', dataIndex: 'timezone', width: 120 },
  { title: '需求时间', dataIndex: 'requirementTime', slotName: 'requirementTime', width: 180 },
  { title: '需求数量', dataIndex: 'quantity', slotName: 'quantity', width: 100 },
  { title: '广告户类型', dataIndex: 'typeName', slotName: 'typeName', width: 110 },
  { title: '客户BM ID', dataIndex: 'customerBmId', width: 150, ellipsis: true, tooltip: true },
  { title: '客户邮箱', dataIndex: 'customerEmail', width: 150, ellipsis: true, tooltip: true },
  { title: '开户费', dataIndex: 'payAmount', width: 100 },
  { title: '备注', dataIndex: 'remark', slotName: 'remark', width: 200, ellipsis: true, tooltip: true },
  { title: '状态', dataIndex: 'status', slotName: 'status', width: 100 },
  { title: '处理人', dataIndex: 'handleUserString', width: 120 },
  { title: '完成数量', dataIndex: 'finishQuantity', slotName: 'finishQuantity', width: 100 },
  { title: '取消原因', dataIndex: 'cancelReason', slotName: 'cancelReason', width: 200, ellipsis: true, tooltip: true },
  { title: '创建时间', dataIndex: 'createTime', slotName: 'createTime', width: 180 },
  { title: '创建人', dataIndex: 'createUserString', slotName: 'createUser', width: 120 },
  {
    title: '操作',
    dataIndex: 'action',
    slotName: 'action',
    width: 220,
    align: 'center',
    fixed: !isMobile() ? 'right' : undefined,
    show: has.hasPermOr(['biz:customerRequirement:update', 'biz:customerRequirement:delete']),
  },
])

// 重置
const reset = () => {
  queryForm.customerId = undefined
  queryForm.timezone = undefined
  queryForm.requirementTime = undefined
  queryForm.createTime = undefined
  queryForm.remark = undefined
  queryForm.createUser = undefined
  queryForm.handleUser = undefined
  queryForm.handled = undefined
  queryForm.status = undefined
  search()
}

// 删除
const onDelete = (record: CustomerRequirementResp) => {
  if (record.orderNos?.length) {
    Message.error('该需求已下户，不能删除')
    return
  }

  return handleDelete(() => deleteCustomerRequirement(record.id), {
    content: `是否确定删除该条数据？`,
    showModal: true,
  })
}

const CustomerRequirementAddModalRef = ref<InstanceType<typeof CustomerRequirementAddModal>>()
// 新增
const onAdd = () => {
  CustomerRequirementAddModalRef.value?.onAdd()
}

// 修改
const onUpdate = (record: CustomerRequirementResp) => {
  CustomerRequirementAddModalRef.value?.onUpdate(record.id)
}

onMounted(() => {
  getUserList()
  getCustomerList()
  search()
})

// 完成操作相关的状态
const finishVisible = ref(false)
const finishForm = reactive({
  quantity: undefined as number | undefined,
  record: null as CustomerRequirementResp | null,
})

// 取消操作相关的状态
const cancelVisible = ref(false)
const cancelForm = reactive({
  reason: '',
  record: null as CustomerRequirementResp | null,
})

// 完成操作
const onFinish = (record: CustomerRequirementResp) => {
  finishForm.record = record
  finishForm.quantity = undefined
  finishVisible.value = true
}

const handleFinish = async () => {
  if (!finishForm.quantity) {
    Message.error('请填写完成数量')
    return
  }
  try {
    await finishCustomerRequirement(finishForm.record!.id, finishForm.quantity)
    Message.success('操作成功')
    finishVisible.value = false
    search()
  } catch (error) {
    // 错误处理
  }
}

const handleFinishCancel = () => {
  finishVisible.value = false
  finishForm.quantity = undefined
  finishForm.record = null
}

// 取消操作
const onCancel = (record: CustomerRequirementResp) => {
  cancelForm.record = record
  cancelForm.reason = ''
  cancelVisible.value = true
}

const onInvited = async (record: CustomerRequirementResp) => {
  try {
    await invitedCustomerRequirement(record.id)
    Message.success('操作成功')
    search()
  } catch (error) {
    // 错误处理
  }
}

const onWait = async (record: CustomerRequirementResp) => {
  try {
    await waitCustomerRequirement(record.id)
    Message.success('操作成功')
    search()
  } catch (error) {
    // 错误处理
  }
}

const handleCancel = async () => {
  try {
    await cancelCustomerRequirement(cancelForm.record!.id, cancelForm.reason)
    Message.success('操作成功')
    cancelVisible.value = false
    search()
  } catch (error) {
    // 错误处理
  }
}

const handleCancelCancel = () => {
  cancelVisible.value = false
  cancelForm.reason = ''
  cancelForm.record = null
}

const onCreateOrder = (record: any) => {
  orderModalRef.value?.onOpen(record)
}

const finishQuantity = ref<number>()
const cancelReason = ref<string>('')
</script>

<style scoped lang="scss">
.statistic-container {
  margin-top: 10px;
  margin-bottom: 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  text-align: center
}
a-statistic {
  flex: 1 1 auto;
  min-width: 200px;
}
</style>
