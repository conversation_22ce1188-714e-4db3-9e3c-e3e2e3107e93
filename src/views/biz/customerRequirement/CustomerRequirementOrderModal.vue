<template>
  <a-modal
    v-model:visible="visible"
    title="创建下户订单"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 600 ? 600 : '100%'"
    draggable
    @before-ok="save"
    @close="reset"
  >
    <GiForm ref="formRef" v-model="form" :options="options" :columns="columns" />
  </a-modal>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import { useWindowSize } from '@vueuse/core'
import { batchAddAdAccountOrder, batchAddAdAccountOrderByAdAccountId } from '@/apis/biz/adAccountOrder'
import { type Columns, GiForm, type Options } from '@/components/GiForm'
import { useResetReactive } from '@/hooks'
import { useDict } from '@/hooks/app'
import { useCustomer } from '@/hooks/app/useCustomer'
import type { LabelValueState } from '@/types/global'
import { listBusinessManagerDict, listProfitTypeDict } from '@/apis'

const emit = defineEmits<{
  (e: 'save-success'): void
}>()

const { width } = useWindowSize()
const visible = ref(false)
const formRef = ref<InstanceType<typeof GiForm>>()

const options: Options = {
  form: { size: 'large' },
  btns: { hide: true },
}

const yesOrNoOptions = [{
  label: '是',
  value: true,
}, {
  label: '否',
  value: false,
}]

const profitTypeList = ref<LabelValueState[]>([])

const getProfitTypeList = async () => {
  const { data } = await listProfitTypeDict({ adPlatform: '1', cat: 'ad_account' })
  profitTypeList.value = data
}

const { timezone_type } = useDict('timezone_type')
const { customerList, getCustomerList } = useCustomer()

// 修改表单初始值
const [form, resetForm] = useResetReactive({
  orderType: 1,
  customerId: undefined,
  customerBmId: '',
  bmType: undefined,
  timeZone: '',
  useCleanBm5: 0,
  payAmount: undefined,
  payTime: undefined,
  adAccountName: '',
  remark: '',
  customerRequirementId: undefined,
  purchasedQuantity: undefined, // 新增购买数量字段
  bm1Id: undefined,
})

const bm1List = ref<LabelValueState[]>([])
const getBm1List = async () => {
  const { data } = await listBusinessManagerDict({ type: '7' })
  bm1List.value = data
}
getBm1List()
const orderTypeOptions = [{
  label: '广告户类型',
  value: 1,
}, {
  label: '广告户ID',
  value: 2,
}]
// 修改表单字段配置
const columns: Columns = reactive<Columns>([
  {
    label: '下户方式',
    field: 'orderType',
    type: 'radio-group',
    options: orderTypeOptions,
    rules: [{ required: true, message: '请选择下户方式' }],
  },
  {
    label: '关联客户',
    field: 'customerId',
    type: 'select',
    options: customerList,
    rules: [{ required: true, message: '请选择关联客户' }],
    props: {
      allowSearch: true,
      disabled: true, // 设置为只读
    },
  },
  {
    label: '广告户类型',
    field: 'bmType',
    type: 'select',
    options: profitTypeList,
    rules: [{ required: true, message: '请选择广告户类型' }],
    hide: () => form.orderType === 2,
  },
  {
    label: '关联广告户',
    field: 'adAccountIds',
    type: 'textarea',
    props: {
      placeholder: '请输入广告户ID，多个ID用逗号或换行隔开',
      autoSize: { minRows: 2, maxRows: 6 },
    },
    rules: [{ required: true, message: '请输入广告户ID' }],
    hide: () => form.orderType === 1,
  },
  {
    label: '是否低限户',
    field: 'isLowLimit',
    type: 'radio-group',
    options: yesOrNoOptions,
    hide: () => form.orderType === 2,
  },
  {
    label: '要求vo',
    field: 'requireVo',
    type: 'radio-group',
    options: yesOrNoOptions,
    hide: () => form.orderType === 2,
  },
  {
    label: '授权BM1',
    field: 'bm1Id',
    type: 'select',
    options: bm1List,
    hide: () => form.bmType !== -1,
    props: {
      allowSearch: true,
    },
  },
  {
    label: '时区',
    field: 'timeZone',
    type: 'select',
    options: timezone_type,
    rules: [{ required: true, message: '请选择时区' }],
    props: {
      disabled: true, // 设置为只读
    },
    hide: () => form.orderType === 2,
  },

  {
    label: '客户BM ID',
    field: 'customerBmId',
    type: 'input',
    rules: [{ required: true, message: '请输入客户BM ID' }],
  },
  {
    label: '开户费',
    field: 'payAmount',
    type: 'input-number',
    rules: [{ required: true, message: '请输入开户费' }],
  },
  {
    label: '下户时间',
    field: 'payTime',
    type: 'date-picker',
    props: {
      showTime: true,
    },
  },
  {
    label: '购买数量',
    field: 'purchasedQuantity',
    type: 'input-number',
    props: {
      min: 1,
      precision: 0,
    },
    rules: [{ required: true, message: '请输入购买数量' }],
  },
  {
    label: '广告户名字',
    field: 'adAccountName',
    type: 'textarea',
    formItemProps: {
      help: '名字和购买数量一样用,隔开',
    },
    props: {
      autoSize: { minRows: 2, maxRows: 6 },
    },
  },
  {
    label: '备注',
    field: 'remark',
    type: 'textarea',
    props: {
      autoSize: true,
    },
  },
])

// 重置
const reset = () => {
  formRef.value?.formRef?.resetFields()
  resetForm()
}

// 保存
const save = async () => {
  try {
    const isInvalid = await formRef.value?.formRef?.validate()
    if (isInvalid) return false

    if (form.orderType === 1) {
      await batchAddAdAccountOrder(form)
    } else {
      // 广告户ID下户
      const adAccountIds = form.adAccountIds.split(/[,，\s]+/)
        .filter(Boolean)
        .map((id) => id.trim())
      let adAccountNames: string[] = []

      if (form.adAccountName) {
        adAccountNames = form.adAccountName.split(/[,，\s]+/)
          .filter(Boolean)
          .map((name) => name.trim())
        if (adAccountNames.length > 0 && adAccountIds.length !== adAccountNames.length) {
          Message.error('关联广告户和广告户名字数量不一致')
          return false
        }
      }

      await batchAddAdAccountOrderByAdAccountId({
        customerId: form.customerId,
        customerBmId: form.customerBmId,
        payAmount: form.payAmount,
        payTime: form.payTime,
        remark: form.remark,
        adAccountIds,
        adAccountNames: adAccountNames.length > 0 ? adAccountNames : undefined,
      })
    }
    Message.success('新增成功')
    emit('save-success')
    return true
  } catch (error) {
    return false
  }
}

// 打开弹框并填充数据
const onOpen = async (record: any) => {
  reset()
  visible.value = true
  form.customerId = record.customerId
  form.customerBmId = record.customerBmId || ''
  form.bmType = record.bmType
  form.customerRequirementId = record.id
  form.timeZone = record.timezone
  form.payAmount = record.payAmount
  form.adAccountName = record.adAccountName || ''
  form.purchasedQuantity = record.quantity || ''
  form.adAccountName = record.adAccountName || ''
  await getProfitTypeList()
  await getCustomerList()
}

defineExpose({ onOpen })
</script>
