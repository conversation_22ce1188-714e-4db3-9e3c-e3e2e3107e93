<template>
  <div class="table-page">
    <div class="table-header mb-4">
      <a-popover position="right">
        <a-button type="text">
          <template #icon><icon-info-circle /></template>
          字段说明
        </a-button>
        <template #content>
          <div class="field-rules">
            <div>剩余正常库存：待出售/特供/已回收、绑定BM5成功的正常广告户 + </div>
            <div>当日回收数量：回收状态的下户订单数(按回收时间统计)</div>
            <div>当天剩余坑位：当天新建数 + 前一天的未使用正常数</div>
            <div>当天接入坑位：当天已使用数（按使用时间统计）+ 当天未使用封禁数（按封禁时间统计）</div>
            <div>总成本：当天接入坑位的单价总和</div>
            <div>已使用正常：按使用时间统计</div>
            <div>未使用正常：总数（不限时间）</div>
            <div>已使用封禁：按使用时间统计</div>
            <div>未使用封禁：按封禁时间统计</div>
            <div>剩余正常坑位：已使用正常 + 未使用正常</div>
            <div>平均备户成本：总成本 / 已使用正常</div>
            <div>当日封禁：绑定BM5成功、状态封禁、封禁时间为当天的广告户数</div>
            <div>当日出售：状态为授权完成，且完成时间为当天的下户订单数（包括大黑号下户）</div>
            <div>当日出售BM户：状态为授权完成，且完成时间为当天的BM户订单数</div>
            <div>下户成本：(当日封禁号成本+当日授权完成号成本) / 当日出售BM户（状态为授权完成）</div>
            <div>存活率：已使用正常 / 当天接入坑位</div>
          </div>
        </template>
      </a-popover>
    </div>
    <GiTable
      title="成本分析统计"
      row-key="id"
      :data="dataList"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
      :pagination="pagination"
      :disabled-tools="['size']"
      :disabled-column-keys="['name']"
      :summary="summaryRowData"
      @refresh="search"
    >
      <template #toolbar-left>
        <DateRangePicker v-model="queryForm.statisticsDate" format="YYYY-MM-DD" @change="customSearch" />
        <a-select
          v-model="queryForm.sortField"
          placeholder="排序字段"
          style="width: 150px; margin-right: 16px"
          allow-clear
          @change="handleSortChange"
        >
          <a-option value="remainingNormalInventory">剩余正常库存</a-option>
          <a-option value="dailyRecycleCount">当日回收数量</a-option>
          <a-option value="dailyReceivedCount">当天接入坑位</a-option>
          <a-option value="totalCost">总成本</a-option>
          <a-option value="usedNormal">已使用正常</a-option>
          <a-option value="unusedNormal">未使用正常</a-option>
          <a-option value="usedBanned">已使用封禁</a-option>
          <a-option value="unusedBanned">未使用封禁</a-option>
          <a-option value="dailyRemainingCount">当天剩余坑位</a-option>
          <a-option value="remainingNormalCount">剩余正常坑位</a-option>
          <a-option value="survivalRate">存活率</a-option>
          <a-option value="averagePrepareCost">平均备户成本</a-option>
          <a-option value="dailySales">当日出售</a-option>
          <a-option value="dailySalesForBm">当日出售BM户</a-option>
          <a-option value="dailyBanned">当日封禁</a-option>
          <a-option value="orderCost">下户成本</a-option>
        </a-select>
        <a-select
          v-model="queryForm.ascSortFlag"
          placeholder="排序方向"
          allow-clear
          style="width: 120px; margin-right: 16px"
          @change="handleSortChange"
        >
          <a-option :value="true">升序</a-option>
          <a-option :value="false">降序</a-option>
        </a-select>
        <a-button @click="reset">
          <template #icon><icon-refresh /></template>
          <template #default>重置</template>
        </a-button>
      </template>
      <template #toolbar-right>
        <a-button @click="onExport">
          <template #icon><icon-download /></template>
          <template #default>导出</template>
        </a-button>
      </template>
      <template #remainingNormalInventory="{ record }">
        <a-popover>
          <template #content>
            <a-space direction="vertical" size="mini">
              <div>
                回收： {{ record.inventoryDetailJson.recycle }}
              </div>
              <div>
                申诉： {{ record.inventoryDetailJson.appeal }}
              </div>
              <div>
                BM5： {{ record.inventoryDetailJson.bm5 }}
              </div>
              <div>
                BM250： {{ record.inventoryDetailJson.bm250 }}
              </div>
              <div>
                BM2500： {{ record.inventoryDetailJson.bm2500 }}
              </div>
              <div>
                BM10000： {{ record.inventoryDetailJson.bm10000 }}
              </div>
              <div>
                短链： {{ record.inventoryDetailJson.short }}
              </div>
            </a-space>
          </template>
          <a-button type="text">{{ record.remainingNormalInventory }}</a-button>
        </a-popover>
      </template>
      <template #action="{ record }">
        <a-link title="同步" @click="onSync(record)">同步</a-link>
      </template>
    </GiTable>
  </div>
</template>

<script setup lang="ts">
import {
  type BusinessManagerStatisticsResp,
  type BusinessManagerStatisticsSummaryResp, syncBusinessManagerStatistics,
} from '@/apis/biz/businessManagerStatistics'
import {
  exportBusinessManagerStatistics,
  getBusinessManagerStatisticsSummary,
  listBusinessManagerStatistics,
} from '@/apis/biz/businessManagerStatistics'
import type { TableInstanceColumns } from '@/components/GiTable/type'
import { useDownload, useTable } from '@/hooks'
import { isMobile } from '@/utils'
import {Message} from "@arco-design/web-vue";

defineOptions({ name: 'BusinessManagerStatistics' })

const queryForm = reactive<any>({
  statisticsDate: undefined,
  sortField: undefined,
  ascSortFlag: false,
  sort: ['id,desc'],
})

const summaryData = ref<BusinessManagerStatisticsSummaryResp>({
  receiveBMItem: 0,
  receiveCost: 0,
  usedNormalBmItem: 0,
  normalRate: 0,
  purchaseCost: 0,
  avgPurchaseCostForDay: 0,
  avgPrepareCost: 0,
})

const {
  tableData: dataList,
  loading,
  pagination,
  search,
} = useTable((page) => listBusinessManagerStatistics({ ...queryForm, ...page }), { immediate: true })
const columns = ref<TableInstanceColumns[]>([
  { title: '统计日期', dataIndex: 'statisticsDate', slotName: 'statisticsDate', width: 120 },
  { title: '采购成本', dataIndex: 'purchaseCost', slotName: 'purchaseCost', width: 140, align: 'center' },
  { title: '剩余库存', dataIndex: 'remainingNormalInventory', slotName: 'remainingNormalInventory', width: 100, align: 'center' },
  { title: '当日回收数量', dataIndex: 'dailyRecycleCount', slotName: 'dailyRecycleCount', width: 120, align: 'center' },
  { title: '当天接入坑位', dataIndex: 'dailyReceivedCount', slotName: 'dailyReceivedCount', width: 120, align: 'center' },
  { title: '总成本', dataIndex: 'totalCost', slotName: 'totalCost', width: 100, align: 'center' },
  { title: '已使用正常', dataIndex: 'usedNormal', slotName: 'usedNormal', width: 120, align: 'center' },
  { title: '未使用正常', dataIndex: 'unusedNormal', slotName: 'unusedNormal', width: 120, align: 'center' },
  { title: '已使用封禁', dataIndex: 'usedBanned', slotName: 'usedBanned', width: 120, align: 'center' },
  { title: '未使用封禁', dataIndex: 'unusedBanned', slotName: 'unusedBanned', width: 120, align: 'center' },
  { title: '当天剩余坑位', dataIndex: 'dailyRemainingCount', slotName: 'dailyRemainingCount', width: 120, align: 'center' },
  { title: '剩余正常坑位', dataIndex: 'remainingNormalCount', slotName: 'remainingNormalCount', width: 120, align: 'center' },
  {
    title: '存活率',
    dataIndex: 'survivalRate',
    slotName: 'survivalRate',
    width: 100,
    render: ({ record }) => record.survivalRate ? `${record.survivalRate}%` : '-',
    align: 'center',
  },
  { title: '平均备户成本', dataIndex: 'averagePrepareCost', slotName: 'averagePrepareCost', width: 120, align: 'center' },
  { title: '当日出售', dataIndex: 'dailySales', slotName: 'dailySales', width: 90, align: 'center' },
  { title: '当日出售BM户', dataIndex: 'dailySalesForBm', slotName: 'dailySalesForBm', width: 90, align: 'center' },
  { title: '当日封禁', dataIndex: 'dailyBanned', slotName: 'dailyBanned', width: 90, align: 'center' },
  { title: '下户成本', dataIndex: 'orderCost', slotName: 'orderCost', width: 90, align: 'center' },
  {
    title: '操作',
    dataIndex: 'action',
    slotName: 'action',
    width: 160,
    align: 'center',
    fixed: !isMobile() ? 'right' : undefined,
  },
])

// 重置
const reset = () => {
  queryForm.statisticsDate = undefined
  queryForm.sortField = undefined
  queryForm.ascSortFlag = false
  queryForm.sort = ['id,desc']
  search()
}

// 处理排序变化
const handleSortChange = () => {
  if (queryForm.sortField) {
    const direction = queryForm.ascSortFlag ? 'asc' : 'desc'
    queryForm.sort = [`${queryForm.sortField},${direction}`]
  } else {
    queryForm.sort = ['id,desc']
  }
  search()
}

const getSummary = async () => {
  const { data } = await getBusinessManagerStatisticsSummary(queryForm)
  summaryData.value = data
}

const customSearch = () => {
  search()
  getSummary()
}

const summaryRowData = () => {
  return [{
    statisticsDate: '合计',
    purchaseCost: `总${summaryData.value.purchaseCost}|平均${summaryData.value.avgPurchaseCostForDay}`,
    dailyReceivedCount: summaryData.value.receiveBMItem,
    totalCost: summaryData.value.receiveCost,
    usedNormal: summaryData.value.usedNormalBmItem,
    survivalRate: summaryData.value.normalRate,
    averagePrepareCost: summaryData.value.avgPrepareCost,
  }]
}

// 导出
const onExport = () => {
  useDownload(() => exportBusinessManagerStatistics(queryForm))
}
getSummary()

const onSync = async (record: BusinessManagerStatisticsResp) => {
  await syncBusinessManagerStatistics(record.id)
  Message.success('同步成功')
  search()
}
</script>

<style scoped lang="scss">
.mb-4 {
  margin-bottom: 16px;
}

.table-header {
  display: flex;
  align-items: center;
}

.field-rules {
  font-size: 13px;
  line-height: 1.6;
  color: var(--color-text-2);
  max-width: 500px;

  > div {
    margin-bottom: 4px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
