<template>
  <div class="table-page">
    <GiTable
      title="工作记录管理"
      row-key="id"
      :data="dataList"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
      :pagination="pagination"
      :disabled-tools="['size']"
      :disabled-column-keys="['name']"
      @refresh="search"
    >
      <template #top>
        <a-descriptions :column="2" v-for="(item,index) in operator_task_type" :key="index">
          <a-descriptions-item :label="item.label">{{getSummaryData(item.value)}}</a-descriptions-item>
        </a-descriptions>
      </template>

      <template #toolbar-left>
        <a-input-search v-model="queryForm.platformAdId" placeholder="请输入广告户" allow-clear @search="search" />
        <a-select
          v-model="queryForm.type"
          :options="operator_task_type"
          placeholder="请选择类型"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <OperationUserSelect v-model="queryForm.createUser" @change="search"></OperationUserSelect>
        <DateRangePicker v-model="queryForm.createTime" @change="search" />
        <a-button @click="reset">
          <template #icon><icon-refresh /></template>
          <template #default>重置</template>
        </a-button>
      </template>
      <template #toolbar-right>
        <a-button v-permission="['biz:operatorTaskRecord:add']" type="primary" @click="onAdd">
          <template #icon><icon-plus /></template>
          <template #default>新增</template>
        </a-button>
        <a-button v-permission="['biz:operatorTaskRecord:export']" @click="onExport">
          <template #icon><icon-download /></template>
          <template #default>导出</template>
        </a-button>
        <a-button  @click="stat">
          <template #icon><icon-bar-chart /></template>
          <template #default>统计</template>
        </a-button>
      </template>
      <template #type="{ record }">
        <GiCellTag :value="record.type" :dict="operator_task_type" />
      </template>
      <template #action="{ record }">
        <a-space>
          <a-link
            v-permission="['biz:operatorTaskRecord:delete']"
            status="danger"
            :disabled="record.disabled"
            :title="record.disabled ? '不可删除' : '删除'"
            @click="onDelete(record)"
          >
            删除
          </a-link>
        </a-space>
      </template>
    </GiTable>

    <OperatorTaskRecordAddModal ref="OperatorTaskRecordAddModalRef" @save-success="search"  />
    <a-modal v-model:visible="visible"   :footer="false" :width="1200">
      <OperatorTaskStat ></OperatorTaskStat>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import OperatorTaskRecordAddModal from './OperatorTaskRecordAddModal.vue'
import OperatorTaskStat from '../operatorTaskStat/index.vue'
import {
  type OperatorTaskRecordQuery,
  type OperatorTaskRecordResp,
  deleteOperatorTaskRecord,
  exportOperatorTaskRecord,
  listOperatorTaskRecord,
  type SummaryStat, getSummaryStatRecord
} from '@/apis/biz/operatorTaskRecord'
import type { TableInstanceColumns } from '@/components/GiTable/type'
import { useDownload, useTable } from '@/hooks'
import { useDict } from '@/hooks/app'
import { isMobile } from '@/utils'
import has from '@/utils/has'

defineOptions({ name: 'OperatorTaskRecord' })

const { operator_task_type } = useDict('operator_task_type')

const queryForm = reactive<OperatorTaskRecordQuery>({
  platformAdId: undefined,
  type: undefined,
  createUser: undefined,
  createTime: undefined,
  sort: ['id,desc'],
})

const SummaryStatList = ref<SummaryStat[]>()
const getSummaryStat = async () => {
   const { data } = await getSummaryStatRecord({...queryForm});
   SummaryStatList.value = data
}

getSummaryStat()

const getSummaryData = (id : number) =>{
  return SummaryStatList.value?.find(item => item.type == id)?.count ?? 0
}
const {
  tableData: dataList,
  loading,
  pagination,
  search : tableSearch,
  handleDelete,
} = useTable((page) => listOperatorTaskRecord({ ...queryForm, ...page }), { immediate: true })
const columns = ref<TableInstanceColumns[]>([
  {
    title: '序号',
    width: 66,
    align: 'center',
    render: ({ rowIndex }) => h('span', {}, rowIndex + 1 + (pagination.current - 1) * pagination.pageSize),
    fixed: !isMobile() ? 'left' : undefined,
  },
  { title: '广告户', dataIndex: 'platformAdId', slotName: 'platformAdId' },
  { title: '类型', dataIndex: 'type', slotName: 'type', align: 'center' },
  { title: '数量', dataIndex: 'num', slotName: 'num', align: 'center' },
  { title: '备注', dataIndex: 'remark', slotName: 'remark' },
  { title: '创建人', dataIndex: 'createUserString', slotName: 'createUser' },
  { title: '创建时间', dataIndex: 'createTime', slotName: 'createTime' },
  {
    title: '操作',
    dataIndex: 'action',
    slotName: 'action',
    width: 160,
    align: 'center',
    fixed: !isMobile() ? 'right' : undefined,
    show: has.hasPermOr(['biz:operatorTaskRecord:detail', 'biz:operatorTaskRecord:update', 'biz:operatorTaskRecord:delete']),
  },
])
const search = async () => {
   tableSearch()
  await getSummaryStat()
}
// 重置
const reset = () => {
  queryForm.platformAdId = undefined
  queryForm.type = undefined
  queryForm.createUser = undefined
  queryForm.createTime = undefined
  search()
}

// 删除
const onDelete = (record: OperatorTaskRecordResp) => {
  return handleDelete(() => deleteOperatorTaskRecord(record.id), {
    content: `是否确定删除该条数据？`,
    showModal: true,
  })
}

// 导出
const onExport = () => {
  useDownload(() => exportOperatorTaskRecord(queryForm))
}

const OperatorTaskRecordAddModalRef = ref<InstanceType<typeof OperatorTaskRecordAddModal>>()
// 新增
const onAdd = () => {
  OperatorTaskRecordAddModalRef.value?.onAdd()
}

const visible = ref<boolean>(false)

const stat = () => {
  visible.value = true
}

</script>

<style scoped lang="scss"></style>
