<template>
  <div class="table-page">
    <GiTable
      v-model:selected-keys="selectedKeys"
      title="采购订单管理"
      row-key="id"
      :data="dataList"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
      :pagination="pagination"
      :disabled-tools="['size']"
      :disabled-column-keys="['name']"
      :row-selection="{
        type: 'checkbox',
        showCheckedAll: true,
        onlyCurrent: true,
      }"
      @refresh="search"
    >
      <template #toolbar-left>
        <a-select
          v-model="queryForm.adPlatform"
          :options="ad_platform"
          placeholder="请选择媒体平台"
          allow-clear
          style="width: 150px"
          @change="onAdPlatformChange"
        />
        <a-select
          v-model="queryForm.project"
          :options="ad_project"
          placeholder="请选择项目"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <a-select
          v-model="queryForm.type"
          :options="profitTypeList"
          placeholder="请先选择广告平台"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <a-select
          v-model="queryForm.channelId"
          :options="BMChannelList"
          placeholder="请选择渠道"
          allow-search
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <TrueAndFalseSelect v-model="queryForm.isPay" placeholder="请选择是否付款" @change="search" />
        <a-select
          v-model="queryForm.status"
          :options="purchase_order_status"
          placeholder="请选择状态"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <DateRangePicker v-model="queryForm.createTime" @change="search" />
        <DateRangePicker v-model="queryForm.purchaseTime" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :placeholder="['购买时间', '购买时间']" @change="search" />
        <a-button @click="reset">
          <template #icon><icon-refresh /></template>
          <template #default>重置</template>
        </a-button>
      </template>
      <template #toolbar-right>
        <a-button v-permission="['biz:purchaseOrder:add']" type="primary" @click="onAdd">
          <template #icon><icon-plus /></template>
          <template #default>新增</template>
        </a-button>
        <a-button type="primary" status="success" :disabled="selectedKeys.length === 0" @click="onPay">
          <template #icon><icon-safe /></template>
          <template #default>合并支付</template>
        </a-button>
        <a-popconfirm content="确认批量完成以下所选订单？" @ok="onFinish">
          <a-button type="primary" status="success" :disabled="selectedKeys.length === 0">
            <template #icon><icon-safe /></template>
            <template #default>批量完成</template>
          </a-button>
        </a-popconfirm>
        <a-button @click="onCheck">
          入库核对
        </a-button>
        <a-button v-permission="['biz:purchaseOrder:export']" @click="onExport">
          <template #icon><icon-download /></template>
          <template #default>导出</template>
        </a-button>
      </template>
      <template #type="{ record }">
        <GiCellTag :value="record.type" :dict="purchase_order_type" />
      </template>
      <template #status="{ record }">
        <GiCellTag :value="record.status" :dict="purchase_order_status" />
      </template>
      <template #adPlatform="{ record }">
        <GiCellTag :value="record.adPlatform" :dict="ad_platform" />
      </template>
      <template #project="{ record }">
        <GiCellTag :value="record.project" :dict="ad_project" />
      </template>
      <template #isPay="{ record }">
        <a-tag v-if="record.isPay" color="green" size="small">是</a-tag>
        <a-tag v-else color="red" size="small">否</a-tag>
      </template>
      <template #writeNum="{ record }">
        <div>{{ record.openReceive ? record.writeNum : '无需入库' }}</div>
      </template>
      <template #writePrice="{ record }">
        <div>{{ record.openReceive ? record.writePrice : '无需入库' }}</div>
      </template>
      <template #action="{ record }">
        <a-space>
          <a-link v-if="record.status !== 2" v-permission="['biz:purchaseOrder:update']" title="修改" @click="onUpdate(record)">修改</a-link>
          <a-link v-if="record.status !== 2" v-permission="['biz:purchaseOrder:update']" title="验收" @click="onAddReveiveOrder(record.id)">验收</a-link>
          <a-link
            v-permission="['biz:purchaseOrder:delete']"
            status="danger"
            :disabled="record.disabled"
            :title="record.disabled ? '不可删除' : '删除'"
            @click="onDelete(record)"
          >
            删除
          </a-link>
        </a-space>
      </template>
    </GiTable>

    <PurchaseOrderAddModal ref="PurchaseOrderAddModalRef" @save-success="search" />
    <PurchaseOrderPayModal ref="PurchaseOrderPayModalRef" @save-success="search" />
    <PurchaseReceiveOrderAddModal ref="PurchaseReceiveOrderAddModalRef" @save-success="search" />
    <PurchaseOrderCheckModal ref="PurchaseOrderCheckModalRef" />
  </div>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import PurchaseOrderAddModal from './PurchaseOrderAddModal.vue'
import {
  type PurchaseOrderQuery,
  type PurchaseOrderResp,
  deletePurchaseOrder,
  exportPurchaseOrder,
  finishPurchaseOrder,
  listPurchaseOrder,
} from '@/apis/biz/purchaseOrder'
import type { TableInstanceColumns } from '@/components/GiTable/type'
import { useDownload, useTable } from '@/hooks'
import { useDict } from '@/hooks/app'
import { isMobile } from '@/utils'
import has from '@/utils/has'
import PurchaseOrderPayModal from '@/views/biz/purchaseOrder/PurchaseOrderPayModal.vue'
import { listBusinessManagerChannelDict, listProfitTypeDict } from '@/apis'
import type { LabelValueState } from '@/types/global'
import PurchaseReceiveOrderAddModal from '@/views/biz/purchaseOrder/PurchaseReceiveOrderAddModal.vue'
import PurchaseOrderCheckModal from '@/views/biz/purchaseOrder/PurchaseOrderCheckModal.vue'

defineOptions({ name: 'PurchaseOrder' })

const { ad_project, ad_platform, purchase_order_status, purchase_order_type } = useDict('ad_project', 'ad_platform', 'purchase_order_status', 'purchase_order_type')

const queryForm = reactive<PurchaseOrderQuery>({
  adPlatform: undefined,
  project: undefined,
  channelId: undefined,
  type: undefined,
  isPay: undefined,
  status: undefined,
  createTime: undefined,
  purchaseTime: undefined,
  sort: ['createTime,desc'],
})

const profitTypeList = ref<LabelValueState[]>([])

const getProfitTypeList = async () => {
  const { data } = await listProfitTypeDict({ adPlatform: queryForm.adPlatform })
  profitTypeList.value = data
}

const onAdPlatformChange = () => {
  queryForm.type = undefined
  profitTypeList.value = []
  if (queryForm.adPlatform) {
    getProfitTypeList()
  }
}

const {
  tableData: dataList,
  loading,
  pagination,
  search,
  selectedKeys,
  handleDelete,
} = useTable((page) => listPurchaseOrder({ ...queryForm, ...page }), { immediate: true })
const columns = ref<TableInstanceColumns[]>([
  {
    title: '序号',
    width: 66,
    align: 'center',
    render: ({ rowIndex }) => h('span', {}, rowIndex + 1 + (pagination.current - 1) * pagination.pageSize),
    fixed: !isMobile() ? 'left' : undefined,
  },
  { title: '媒体平台', dataIndex: 'adPlatform', slotName: 'adPlatform', align: 'center', width: 100 },
  { title: '项目', dataIndex: 'project', slotName: 'project', align: 'center', width: 100 },
  { title: '物料类型', dataIndex: 'typeName', slotName: 'typeName', align: 'center', width: 160 },
  { title: '物料渠道', dataIndex: 'channelName', slotName: 'channelName', width: 180 },
  { title: '数量', dataIndex: 'num', slotName: 'num', children: [{
    title: '采购数量',
    dataIndex: 'expectNum',
    align: 'center',
    width: 90,
  }, {
    title: '验收数量',
    dataIndex: 'receiveNum',
    align: 'center',
    width: 90,
  }, {
    title: '入库数量',
    dataIndex: 'writeNum',
    slotName: 'writeNum',
    align: 'center',
    width: 90,
  }] },
  { title: '金额', dataIndex: 'price', slotName: 'price', children: [{
    title: '采购金额',
    dataIndex: 'totalPrice',
    align: 'center',
    width: 90,
  }, {
    title: '验收金额',
    dataIndex: 'receivePrice',
    align: 'center',
    width: 90,
  }, {
    title: '入库金额',
    dataIndex: 'writePrice',
    slotName: 'writePrice',
    align: 'center',
    width: 90,
  }] },
  { title: '状态', dataIndex: 'status', slotName: 'status', align: 'center', width: 80 },
  { title: '是否付款', dataIndex: 'isPay', slotName: 'isPay', align: 'center', width: 100 },
  { title: '付款时间', dataIndex: 'payTime', slotName: 'payTime', width: 180 },
  { title: '付款金额', dataIndex: 'payPrice', slotName: 'payPrice', align: 'center', width: 100 },
  { title: '备注', dataIndex: 'remark', slotName: 'remark', width: 180, ellipsis: true, tooltip: true },
  { title: '采购时间', dataIndex: 'purchaseTime', slotName: 'purchaseTime', width: 180 },
  { title: '验收时间', dataIndex: 'receiveDate', slotName: 'purchaseTime', width: 180 },
  { title: '完成时间', dataIndex: 'finishTime', slotName: 'finishTime', width: 180 },
  { title: '验收人', dataIndex: 'receiveUser', slotName: 'receiveUser', width: 100 },
  { title: '创建人', dataIndex: 'createUserString', slotName: 'createUser', width: 100 },
  { title: '创建时间', dataIndex: 'createTime', slotName: 'createTime', width: 180 },
  {
    title: '操作',
    dataIndex: 'action',
    slotName: 'action',
    width: 160,
    align: 'center',
    fixed: !isMobile() ? 'right' : undefined,
    show: has.hasPermOr(['biz:purchaseOrder:detail', 'biz:purchaseOrder:update', 'biz:purchaseOrder:delete']),
  },
])

// 重置
const reset = () => {
  queryForm.channelId = undefined
  queryForm.type = undefined
  queryForm.isPay = undefined
  queryForm.status = undefined
  queryForm.createTime = undefined
  search()
}

// 删除
const onDelete = (record: PurchaseOrderResp) => {
  return handleDelete(() => deletePurchaseOrder(record.id), {
    content: `是否确定删除该条数据？`,
    showModal: true,
  })
}

// 导出
const onExport = () => {
  useDownload(() => exportPurchaseOrder(queryForm))
}

const PurchaseOrderAddModalRef = ref<InstanceType<typeof PurchaseOrderAddModal>>()
// 新增
const onAdd = () => {
  PurchaseOrderAddModalRef.value?.onAdd()
}

// 修改
const onUpdate = (record: PurchaseOrderResp) => {
  PurchaseOrderAddModalRef.value?.onUpdate(record.id)
}

const PurchaseOrderPayModalRef = ref<InstanceType<typeof PurchaseOrderPayModal>>()

const onPay = () => {
  const data: PurchaseOrderResp[] = []
  dataList.value.forEach((item) => {
    if (selectedKeys.value.includes(item.id) && !item.isPay) {
      data.push(item)
    }
  })
  PurchaseOrderPayModalRef.value?.onOpen(data)
}

const onFinish = async () => {
  await finishPurchaseOrder({
    ids: selectedKeys.value,
  })
  Message.success('收货成功')
}

const PurchaseReceiveOrderAddModalRef = ref<InstanceType<typeof PurchaseReceiveOrderAddModal>>()
// 新增
const onAddReveiveOrder = (id: string) => {
  PurchaseReceiveOrderAddModalRef.value?.onAdd(id)
}

const BMChannelList = ref<LabelValueState[]>([])

const getBMChannelList = async () => {
  const res = await listBusinessManagerChannelDict()
  BMChannelList.value = res.data
}
getBMChannelList()

const PurchaseOrderCheckModalRef = ref<InstanceType<typeof PurchaseOrderCheckModal>>()
const onCheck = () => {
  PurchaseOrderCheckModalRef.value?.onOpen()
}
</script>

<style scoped lang="scss"></style>
