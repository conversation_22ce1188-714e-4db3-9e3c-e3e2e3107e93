<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    :mask-closable="false"
    :esc-to-close="false"
    :unmount-on-close="true"
    :width="width >= 1100 ? 1100 : '100%'"
    draggable
    @before-ok="save"
    @close="reset"
  >
    <div>
      <a-date-picker
        v-model="payTime"
        placeholder="请选择支付时间，不选则默认当天时间"
        style="width: 300px; margin: 0 24px 24px 0;"
        show-time
        format="YYYY-MM-DD HH:mm:ss"
      />
      <a-checkbox v-model="isFinish">是否完成</a-checkbox>
    </div>
    <a-table
      row-key="id"
      :data="dataList"
      :columns="columns"
      :pagination="false"
      :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
      style="margin-top: 20px"
    >
      <template #payPrice="{ record }">
        <a-input-number v-model="record.payPrice" :min="0" />
      </template>
    </a-table>
    <div style="margin-top: 20px;text-align: right">
      总计：{{ totalPayPrice }}
    </div>
  </a-modal>
</template>

<script setup lang="tsx">
import { useWindowSize } from '@vueuse/core'
import { Message } from '@arco-design/web-vue'
import type { TableInstanceColumns } from '@/components/GiTable/type'
import { type PurchaseOrderResp, payPurchaseOrder } from '@/apis/biz/purchaseOrder'

const emit = defineEmits<{
  (e: 'save-success'): void
}>()

const { width } = useWindowSize()

const visible = ref(false)
const title = computed(() => ('合并支付'))

const payTime = ref('')
const isFinish = ref(false)

const dataList = ref<PurchaseOrderResp[]>([])

const columns = ref<TableInstanceColumns[]>([
  { title: '渠道', dataIndex: 'channelName', slotName: 'channelName' },
  { title: '物料类型', dataIndex: 'typeName', slotName: 'typeName', align: 'center' },
  { title: '验收数量', dataIndex: 'receiveNum', slotName: 'receiveNum', align: 'center' },
  { title: '支付金额', dataIndex: 'payPrice', slotName: 'payPrice', align: 'center' },
])

// 重置
const reset = () => {
  dataList.value = []
  payTime.value = ''
  isFinish.value = false
}

const totalPayPrice = computed(() => {
  let result = 0
  dataList.value.forEach((item) => {
    result += item.payPrice
  })
  return result
})

// 新增
const onOpen = async (data: PurchaseOrderResp[]) => {
  reset()
  dataList.value = data
  visible.value = true
}

const save = async () => {
  try {
    const items: any = []
    for (let i = 0; i < dataList.value.length; i++) {
      if (!dataList.value[i].payPrice) {
        return Message.error('请输入价格')
      }
      items.push({
        id: dataList.value[i].id,
        payPrice: dataList.value[i].payPrice,
      })
    }
    const form = {
      payTime: payTime.value,
      isFinish: isFinish.value,
      items,
    }
    await payPurchaseOrder(form)
    Message.success('支付成功')
    emit('save-success')
    return true
  } catch (error) {
    return false
  }
}

defineExpose({ onOpen })
</script>

<style scoped lang="scss"></style>
