<template>
  <div class="table-page">
    <GiTable
      title="bm管理员管理"
      row-key="id"
      :data="dataList"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
      :pagination="pagination"
      :disabled-tools="['size']"
      :disabled-column-keys="['name']"
      @refresh="search"
    >
      <template #toolbar-left>
        <a-input-search v-model="queryForm.bmId" placeholder="请输入BM ID" allow-clear @search="search" />
        <a-select
          v-model="queryForm.isSelfUser"
          placeholder="是否内部号"
          allow-clear
          style="width: 150px"
          @change="search"
        >
          <a-option label="是" :value="true"></a-option>
          <a-option label="否" :value="false"></a-option>
        </a-select>
        <AdAccountTypeSelect v-model="queryForm.bmType" @change="search"></AdAccountTypeSelect>
        <a-select
          v-model="queryForm.userRole"
          placeholder="用户权限"
          allow-clear
          style="width: 150px"
          @change="search"
        >
          <a-option label="管理员" value="ADMIN"></a-option>
          <a-option label="员工" value="EMPLOYEE"></a-option>
        </a-select>
        <a-select
          v-model="queryForm.isRemove"
          placeholder="是否移除"
          allow-clear
          style="width: 150px"
          @change="search"
        >
          <a-option label="是" :value="true"></a-option>
          <a-option label="否" :value="false"></a-option>
        </a-select>
        <DateRangePicker v-model="queryForm.bmCreateTime" :show-time="false" :placeholder="['BM创建时间', 'BM创建时间']" @change="search" />
        <a-button @click="reset">
          <template #icon><icon-refresh /></template>
          <template #default>重置</template>
        </a-button>
      </template>
      <template #bmBrowser="{ record }">
        <AdsBrowser
          :open-url="getBusinessManagerMainPage(record.bmId)" :browser-no="record.bmBrowser"
          :status="0"
        ></AdsBrowser>
      </template>
      <template #isRemove="{ record }">
        <a-tag v-if="record.isRemove" color="arcoblue" size="small">是</a-tag>
        <a-tag v-else color="red" size="small">否</a-tag>
      </template>
      <template #isSelfUser="{ record }">
        <a-tag v-if="record.isSelfUser" color="arcoblue" size="small">是</a-tag>
        <a-tag v-else color="red" size="small">否</a-tag>
      </template>
      <template #action="{ record }">
        <a-link v-if="record.userEmail && !record.isSelfUser" title="加白" @click="onAddWhiteList(record.userEmail)">加白</a-link>
      </template>
    </GiTable>
  </div>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import {
  type BusinessManagerUserQuery,
  addBusinessManagerUserWhiteList,
  listBusinessManagerUser,
} from '@/apis/biz/businessManagerUser'
import type { TableInstanceColumns } from '@/components/GiTable/type'
import { useTable } from '@/hooks'
import { getBusinessManagerMainPage, isMobile } from '@/utils'

defineOptions({ name: 'BusinessManagerUser' })

const queryForm = reactive<BusinessManagerUserQuery>({
  bmId: undefined,
  isRemove: undefined,
  isSelfUser: undefined,
  bmCreateTime: undefined,
  userRole: undefined,
  bmType: undefined,
  sort: ['id,desc'],
})

const {
  tableData: dataList,
  loading,
  pagination,
  search,
  refresh,
} = useTable((page) => listBusinessManagerUser({ ...queryForm, ...page }), { immediate: true })
const columns = ref<TableInstanceColumns[]>([
  {
    title: '序号',
    width: 66,
    align: 'center',
    render: ({ rowIndex }) => h('span', {}, rowIndex + 1 + (pagination.current - 1) * pagination.pageSize),
    fixed: !isMobile() ? 'left' : undefined,
  },
  { title: 'BM渠道', dataIndex: 'channelName', slotName: 'channelName', width: 120 },
  { title: 'BM ID', dataIndex: 'bmId', slotName: 'bmId', width: 180 },
  { title: 'BM类型', dataIndex: 'typeName', slotName: 'typeName', width: 180 },
  { title: 'BM创建时间', dataIndex: 'bmCreateTime', slotName: 'bmCreateTime', width: 180 },
  { title: '浏览器', dataIndex: 'bmBrowser', slotName: 'bmBrowser', align: 'center', width: 100 },
  { title: 'USER ID', dataIndex: 'userId', slotName: 'userId', width: 180 },
  { title: '用户名', dataIndex: 'username', slotName: 'username', width: 100 },
  { title: '邮箱', dataIndex: 'userEmail', slotName: 'userEmail', width: 200 },
  { title: '用户权限', dataIndex: 'userRole', slotName: 'userRole', width: 100 },
  { title: '内部号', dataIndex: 'isSelfUser', slotName: 'isSelfUser', align: 'center', width: 80 },
  { title: '移除', dataIndex: 'isRemove', slotName: 'isRemove', align: 'center', width: 60 },
  {
    title: '操作',
    dataIndex: 'action',
    slotName: 'action',
    width: 100,
    align: 'center',
    fixed: !isMobile() ? 'right' : undefined,
  },
])

const onAddWhiteList = async (email: string) => {
  if (!email) {
    return
  }
  await addBusinessManagerUserWhiteList({ email })
  Message.success('添加成功')
  refresh()
}

// 重置
const reset = () => {
  queryForm.bmId = undefined
  queryForm.isRemove = undefined
  queryForm.bmType = undefined
  queryForm.isSelfUser = undefined
  queryForm.userRole = undefined
  queryForm.bmCreateTime = undefined
  search()
}
</script>

<style scoped lang="scss"></style>
