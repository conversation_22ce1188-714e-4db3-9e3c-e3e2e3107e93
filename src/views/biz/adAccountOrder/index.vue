<template>
  <div class="table-page">
    <GiTable
      v-model:selectedKeys="selectedKeys"
      :row-selection="{
        type: 'checkbox',
        showCheckedAll: true,
        onlyCurrent: true,
      }"
      row-key="id"
      size="medium"
      column-resizable
      :scrollbar="true"
      columns-cache-key="adAccountOrderList"
      :data="dataList"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
      :pagination="pagination"
      @refresh="refresh"
      @sorter-change="onSorterChange"
    >
      <template #toolbar-left>
        <a-input-search v-model="queryForm.orderNo" placeholder="请输入订单号" allow-clear @search="search" />
        <CustomerSelect v-model="queryForm.customerId" @change="search" />
        <a-textarea
          v-model="queryForm.adAccountIds"
          placeholder="请输入广告户 ID，多个ID可用空格、逗号、换行符分隔"
          allow-clear
          :auto-size="{ minRows: 1, maxRows: 3 }"
          style="width: 300px"
          @change="search"
        />
        <a-select
          v-model="queryForm.channelName" placeholder="请选择渠道" allow-clear allow-search :options="BM5ChannelOption"
          style="width: 150px"
          @change="search"
        />
        <a-textarea
          v-model="queryForm.adAccountBmIds"
          placeholder="请输入BM5 ID，多个ID可用空格、逗号、换行符分隔"
          allow-clear
          :auto-size="{ minRows: 1, maxRows: 3 }"
          style="width: 300px"
          @change="search"
        />
        <AdAccountTypeSelect v-model="queryForm.bmType" multiple @change="search"></AdAccountTypeSelect>
        <a-input-search v-model="queryForm.remark" placeholder="备注" allow-clear @search="search" />
        <a-input-search v-model="queryForm.customerBmId" placeholder="请输入客户BM ID" allow-clear @search="search" />
        <a-select
          v-model="queryForm.handleUser"
          placeholder="请选择处理人"
          :options="userList"
          allow-clear
          allow-search
          style="width: 150px"
          @change="search"
        />
        <a-select
          v-model="queryForm.status"
          :options="ad_account_order_status"
          placeholder="请选择状态"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <a-select
          v-model="queryForm.refunded"
          :options="[{ label: '已退款', value: true }, { label: '未退款', value: false }]"
          placeholder="请选择退款状态"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <!-- 添加账号状态筛选 -->
        <a-select
          v-model="queryForm.accountStatus"
          :options="ad_account_status"
          placeholder="请选择账号状态"
          allow-clear
          multiple
          style="width: 150px"
          @change="search"
        />
        <!-- 添加清零状态筛选 -->
        <a-select
          v-model="queryForm.clearStatus"
          :options="ad_account_clear_status"
          placeholder="请选择清零状态"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <a-select
          v-model="queryForm.timezone"
          :options="timezone_type"
          placeholder="请选择时区"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <a-select
          v-model="queryForm.isOpen"
          placeholder="是否开启广告系列"
          allow-clear
          style="width: 200px"
          @change="search"
        >
          <a-option label="是" :value="true"></a-option>
          <a-option label="否" :value="false"></a-option>
        </a-select>
        <a-input-number v-model="queryForm.campaignTime" placeholder="开启广告户天数(小于)" @change="search" />
        <a-select
          v-model="queryForm.enablePrepay"
          placeholder="是否开启预充"
          allow-clear
          style="width: 200px"
          @change="search"
        >
          <a-option label="是" :value="true"></a-option>
          <a-option label="否" :value="false"></a-option>
        </a-select>
        <a-select
          v-model="queryForm.isOneDollar"
          placeholder="是否一刀流"
          allow-clear
          style="width: 200px"
          @change="search"
        >
          <a-option label="是" :value="true"></a-option>
          <a-option label="否" :value="false"></a-option>
        </a-select>
        <!--        <a-select -->
        <!--          v-model="queryForm.takeStatus" -->
        <!--          placeholder="是否接收" -->
        <!--          allow-clear -->
        <!--          style="width: 200px" -->
        <!--          @change="search" -->
        <!--        > -->
        <!--          <a-option label="是" :value="true"></a-option> -->
        <!--          <a-option label="否" :value="false"></a-option> -->
        <!--        </a-select> -->
        <DateRangePicker v-model="queryForm.payTime" :show-time="false" :placeholder="['下户时间', '下户时间']" @change="search" />
        <DateRangePicker v-model="queryForm.finishTime" :show-time="false" :placeholder="['完成时间', '完成时间']" @change="search" />
        <DateRangePicker v-model="queryForm.recycleTime" :show-time="false" :placeholder="['回收时间', '回收时间']" @change="search" />
        <DateRangePicker v-model="queryForm.bmAuthTime" :show-time="false" :placeholder="['授权BM时间', '授权BM时间']" @change="search" />
        <a-select
          v-model="queryForm.tags"
          placeholder="请选择标签"
          allow-search
          allow-clear
          multiple
          style="width: 200px"
          @change="search"
        >
          <a-option
            v-for="item in tagList"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          />
        </a-select>
        <a-button @click="reset">
          <template #icon><icon-refresh /></template>
          <template #default>重置</template>
        </a-button>
      </template>
      <template #toolbar-right>
        <a-button v-permission="['biz:adAccountOrder:add']" type="primary" @click="onAdd">
          <template #icon><icon-plus /></template>
          <template #default>新增</template>
        </a-button>
        <a-button v-permission="['biz:adAccountOrder:add']" type="primary" @click="onBatchAdd">
          <template #icon><icon-plus /></template>
          <template #default>批量下户</template>
        </a-button>
        <a-button v-permission="['biz:adAccountOrder:handle']" :disabled="selectedKeys.length === 0" type="primary" status="warning" @click="onUpdateCustomerBmId()">
          修改客户BMID
        </a-button>
        <a-button v-permission="['biz:adAccountOrder:handle']" :disabled="selectedKeys.length === 0" type="primary" status="warning" @click="onUpdateAdAccountBm1()">
          修改关联BM1
        </a-button>
        <a-button @click="onTimezoneStatOpen">
          时区统计
        </a-button>
        <a-button @click="onStatisticsOpen">
          运营人员统计
        </a-button>
        <a-button @click="onOrderStatisticsOpen">
          下户订单统计
        </a-button>
        <a-button :disabled="selectedKeys.length === 0" @click="onCopySpent">
          复制消耗
        </a-button>
        <a-button v-permission="['biz:adAccountOrder:export']" @click="onExport">
          <template #icon><icon-download /></template>
          <template #default>导出</template>
        </a-button>
      </template>
      <template #status="{ record }">
        <GiCellTag :value="record.status" :dict="ad_account_order_status" />
      </template>
      <template #accountStatus="{ record }">
        <a-space>
          <GiCellTag :value="record.accountStatus" :dict="ad_account_status" />
          <GiCellTag :value="record.clearStatus" :dict="ad_account_clear_status" />
        </a-space>
      </template>
      <template #adAccountName="{ record }">
        <a-typography-text v-if="record.adAccountName" copyable ellipsis>
          {{ record.adAccountName }}
        </a-typography-text>
      </template>
      <template #browserNo="{ record }">
        <AdsBrowser :open-url="getAdAccountPaymentSettingMainPage(record.adAccountId)" :browser-no="record.browserNo"></AdsBrowser>
      </template>
      <template #adAccountId="{ record }">
        <a-space size="mini" direction="vertical">
          <AdAccountCard :platform-ad-id="record.adAccountId" />
          <a-space v-if="record.orderMethod === 2 || record.isOneDollar" size="mini">
            <a-tag v-if="record.orderMethod === 2" color="gray">买断</a-tag>
            <a-tag v-if="record.isOneDollar" color="red">一刀流</a-tag>
          </a-space>
        </a-space>
      </template>
      <template #bmType="{ record }">
        <GiCellTag :value="record.bmType" :dict="business_manager_type" />
      </template>
      <template #totalSpent="{ record }">
        <a-button type="text" @click="onDailyStatOpen(record.adAccountId)">{{ record.totalSpent }}</a-button>
      </template>
      <template #customerBmId="{ record }">
        <a-typography-paragraph
          :ellipsis="{
            expandable: true,
          }"
        >
          {{ record.customerBmId }}
        </a-typography-paragraph>
      </template>
      <template #cardSpent="{ record }">
        <a-space direction="vertical" size="mini">
          <div>{{ record.cardSpent }}</div>
          <a-tag v-if="record.campaignDesc" color="red">{{ record.campaignDesc }}</a-tag>
        </a-space>
      </template>
      <template #prepay="{ record }">
        <a-space v-if="record.enablePrepay" direction="vertical" size="mini">
          <div>{{ record.prepayAccountBalance }}</div>
          <a-tag color="red">已开启预充</a-tag>
        </a-space>
        <div v-else>
          -
        </div>
      </template>
      <template #action="{ record }">
        <a-space>
          <a-link v-permission="['biz:adAccountOrder:update']" @click="onUpdate(record)">修改</a-link>
          <a-link v-if="record.status === 1" v-permission="['biz:adAccountOrder:handle']" @click="onHandle(record.id)">接单</a-link>
          <a-dropdown v-if="record.status === 2" :popup-max-height="false">
            <a-button type="text">
              完成
              <icon-down />
            </a-button>
            <template #content>
              <a-doption
                @click="onInvalid(record)"
              >
                作废
              </a-doption>
              <a-doption
                @click="onAuthFail(record)"
              >
                授权失败
              </a-doption>
              <a-doption
                @click="onReceiveFail(record)"
              >
                接收失败
              </a-doption>
              <a-doption
                @click="onAuthSuccess(record)"
              >
                授权完成
              </a-doption>
            </template>
          </a-dropdown>
          <a-popconfirm
            content="确认退款？"
            type="warning"
            @ok="onRefund(record)"
          >
            <a-link
              v-if="(record.status === 3 || record.status === 5) && record.payAmount > 0 && !record.refunded"
              v-permission="['biz:adAccountOrder:refund']"
            >
              退款
            </a-link>
          </a-popconfirm>
          <a-link v-if="record.status === 1 || record.status === 2" v-permission="['biz:adAccountOrder:handle']" @click="onCancel(record.id)">取消</a-link>
          <a-popconfirm
            content="确认回收该账号？"
            type="warning"
            @ok="onRecycle(record)"
          >
            <a-link v-if="record.status === 3 && record.accountStatus !== 2" v-permission="['biz:adAccountOrder:handle']">回收</a-link>
          </a-popconfirm>
          <a-popconfirm
            content="确认取消回收该账号？"
            type="warning"
            @ok="onCancelRecycle(record)"
          >
            <a-link v-if="record.status === 5 && record.accountStatus !== 2" v-permission="['biz:adAccountOrder:handle']">取消回收</a-link>
          </a-popconfirm>
          <a-popconfirm
            content="确认取消清零？"
            type="warning"
            @ok="onResetClearStatus(record)"
          >
            <a-link v-if="record.status === 3 && record.clearStatus === 3 && record.accountStatus !== 2" v-permission="['biz:adAccountOrder:handle']">取消清零</a-link>
          </a-popconfirm>
        </a-space>
      </template>

      <!-- 添加退款状态显示 -->
      <template #refunded="{ record }">
        <GiCellTag v-if="record.refunded" value="1" :dict="[{ label: '已退款', value: 1, extra: 'success' }]" />
        <GiCellTag v-else value="0" :dict="[{ label: '未退款', value: 0, extra: 'gray' }]" />
      </template>
      <template #tags="{ record }">
        <Tag :id="record.id" :value="record.tags" :data="tagList" :type="2" @refresh="tagRefresh" />
      </template>
    </GiTable>

    <AdAccountOrderAddModal ref="AdAccountOrderAddModalRef" @save-success="search" />
    <AdAccountOrderUpdateModal ref="AdAccountOrderUpdateRef" @save-success="refresh" />
    <AdAccountOrderImportDrawer ref="AdAccountOrderImportDrawerRef" @save-success="search" />
    <AdAccountOrderBatchAddModal ref="AdAccountOrderBatchAddModalRef" @save-success="search" />
    <AdAccountOrderTimezoneStatModal ref="AdAccountOrderTimezoneStatModalRef" />
    <AdAccountDailyStatModal ref="AdAccountDailyStatModalRef" />
    <AdAccountUpdateBM1Modal ref="AdAccountUpdateBM1ModalRef" @save-success="refresh" />
    <AdAccountOrderOperationUserStatModal ref="AdAccountOrderOperationUserStatModalRef" />
    <AdAccountOrderStatModal ref="AdAccountOrderStatModalRef" />
  </div>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import AdAccountOrderAddModal from './AdAccountOrderAddModal.vue'
import {
  type AdAccountOrderQuery,
  type AdAccountOrderResp,
  authFailAdAccountOrder,
  authSuccessAdAccountOrder,
  cancelAdAccountOrder,
  cancelRecycleOrder,
  exportAdAccountOrder,
  handleAdAccountOrder,
  invalidAdAccountOrder,
  listAdAccountOrder,
  receiveFailAdAccountOrder,
  recycleOrder,
  refundAdAccountOrder,
  resetAdAccountOrderClearStatus,
} from '@/apis/biz/adAccountOrder'
import type { TableInstanceColumns } from '@/components/GiTable/type'
import { useDownload, useTable } from '@/hooks'
import { useDict } from '@/hooks/app'
import { getAdAccountPaymentSettingMainPage, isMobile } from '@/utils'
import has from '@/utils/has'
import AdAccountOrderUpdateModal from '@/views/biz/adAccountOrder/AdAccountOrderUpdateModal.vue'
import AdAccountOrderImportDrawer from '@/views/biz/adAccountOrder/AdAccountOrderImportDrawer.vue'
import { listTagDict, listUserDict } from '@/apis'
import type { LabelValueState } from '@/types/global'
import AdAccountOrderBatchAddModal from '@/views/biz/adAccountOrder/AdAccountOrderBatchAddModal.vue'
import AdAccountOrderTimezoneStatModal from '@/views/biz/adAccountOrder/AdAccountOrderTimezoneStatModal.vue'
import AdAccountDailyStatModal from '@/views/biz/finance/adAccountDailyStatModal.vue'
import AdAccountUpdateBM1Modal from '@/views/biz/adAccount/AdAccountUpdateBM1Modal.vue'
import AdAccountOrderOperationUserStatModal from '@/views/biz/adAccountOrder/AdAccountOrderOperationUserStatModal.vue'
import AdAccountOrderStatModal from '@/views/biz/adAccountOrder/AdAccountOrderStatModal.vue'
import { getBusinessManagerChannelList } from '@/apis/biz/businessManagerChannel'

defineOptions({ name: 'AdAccountOrder' })
const { ad_account_order_status, ad_account_status, ad_account_clear_status, timezone_type, business_manager_type } = useDict('ad_account_order_status', 'ad_account_status', 'ad_account_clear_status', 'timezone_type', 'business_manager_type')

const queryForm = reactive<AdAccountOrderQuery>({
  orderNo: undefined,
  customerId: undefined,
  adAccountId: undefined,
  adAccountBmIds: undefined,
  adAccountIds: undefined,
  remark: undefined,
  adAccountName: undefined,
  customerBmId: undefined,
  payAmount: undefined,
  payTime: undefined,
  finishTime: undefined,
  status: undefined,
  handleUser: undefined,
  accountStatus: undefined,
  clearStatus: undefined,
  timezone: undefined,
  bmType: undefined,
  recycleTime: undefined,
  refunded: undefined,
  tags: undefined,
  isOpen: undefined,
  campaignTime: undefined,
  channelName: undefined,
  enablePrepay: undefined,
  isOneDollar: undefined,
  bmAuthTime: undefined,
  takeStatus: undefined,
  customerBusinessType: undefined,
  sort: ['id,desc'],
})

const {
  tableData: dataList,
  loading,
  pagination,
  selectedKeys,
  search,
  refresh,
} = useTable((page) => listAdAccountOrder({ ...queryForm, ...page }), { immediate: true })
const columns = ref<TableInstanceColumns[]>([
  {
    title: '序号',
    width: 66,
    align: 'center',
    render: ({ rowIndex }) => h('span', {}, rowIndex + 1 + (pagination.current - 1) * pagination.pageSize),
    fixed: !isMobile() ? 'left' : undefined,
  },
  { title: '客户', dataIndex: 'customerName', slotName: 'customerName', align: 'center', width: 100, sortable: {
    sortDirections: ['ascend', 'descend'],
    sorter: true,
  } },
  { title: '状态', dataIndex: 'status', slotName: 'status', align: 'center', width: 100 },
  // { title: '接收状态', dataIndex: 'takeStatus;', slotName: 'takeStatus', align: 'center', width: 100 },
  { title: '标签', dataIndex: 'tags', slotName: 'tags', width: 200, align: 'center' },
  { title: '广告户名称', dataIndex: 'adAccountName', slotName: 'adAccountName', width: 180 },
  { title: '客户BM ID', dataIndex: 'customerBmId', slotName: 'customerBmId', width: 180 },
  { title: '关联渠道', dataIndex: 'channelName', slotName: 'channelName', width: 100 },
  { title: '广告户BM', dataIndex: 'adAccountBmId', slotName: 'adAccountBmId', width: 180 },
  { title: 'BM授权时间', dataIndex: 'bmAuthTime', slotName: 'bmAuthTime', width: 180 },
  { title: 'BM类型', dataIndex: 'bmType', slotName: 'bmType', width: 180 },
  { title: '广告户ID', dataIndex: 'adAccountId', slotName: 'adAccountId', width: 180 },
  { title: '浏览器', dataIndex: 'browserNo', slotName: 'browserNo', width: 100, align: 'center' },
  { title: '时区', dataIndex: 'timezone', slotName: 'timezone', width: 140 },
  { title: '广告户状态', dataIndex: 'accountStatus', slotName: 'accountStatus', width: 120, align: 'center' },
  { title: '总充值', dataIndex: 'rechargeAmount', slotName: 'rechargeAmount', width: 100, align: 'center' },
  { title: 'FB余额', dataIndex: 'fbBalance', slotName: 'fbBalance', width: 100, align: 'center' },
  { title: '预充设置', dataIndex: 'prepay', slotName: 'prepay', width: 100, align: 'center' },
  { title: '总消耗', dataIndex: 'totalSpent', slotName: 'totalSpent', width: 100, align: 'center' },
  { title: '当前消耗', dataIndex: 'amountSpent', slotName: 'amountSpent', width: 120, align: 'center', sortable: {
    sortDirections: ['ascend', 'descend'],
    sorter: true,
  } },
  { title: '卡台消耗', dataIndex: 'cardSpent', slotName: 'cardSpent', width: 140, align: 'center', sortable: {
    sortDirections: ['ascend', 'descend'],
    sorter: true,
  } },
  { title: '开户费', dataIndex: 'payAmount', slotName: 'payAmount', align: 'center', width: 80 },
  { title: '处理人', dataIndex: 'handleUserName', slotName: 'handlerUseName', width: 180 },
  { title: '备注', dataIndex: 'remark', slotName: 'remark', width: 100, ellipsis: true, tooltip: true },
  { title: '退款状态', dataIndex: 'refunded', slotName: 'refunded', width: 100, align: 'center' },
  { title: '备户成本', dataIndex: 'accountCost', slotName: 'accountCost', width: 100, align: 'center' },
  { title: '下户成本', dataIndex: 'cost', slotName: 'cost', width: 110, align: 'center' },
  { title: '完成时间', dataIndex: 'finishTime', slotName: 'finishTime', width: 180 },
  { title: '回收时间', dataIndex: 'recycleTime', slotName: 'recycleTime', width: 180 },
  { title: '创建时间', dataIndex: 'createTime', slotName: 'createTime', width: 180 },
  {
    title: '操作',
    dataIndex: 'action',
    slotName: 'action',
    width: 260,
    align: 'center',
    fixed: !isMobile() ? 'right' : undefined,
    show: has.hasPermOr(['biz:adAccountOrder:handle']),
  },
])

// 重置
const reset = () => {
  queryForm.orderNo = undefined
  queryForm.customerId = undefined
  queryForm.adAccountId = undefined
  queryForm.adAccountBmIds = undefined
  queryForm.adAccountIds = undefined
  queryForm.remark = undefined
  queryForm.adAccountName = undefined
  queryForm.customerBmId = undefined
  queryForm.payAmount = undefined
  queryForm.payTime = undefined
  queryForm.finishTime = undefined
  queryForm.status = undefined
  queryForm.handleUser = undefined
  queryForm.accountStatus = undefined
  queryForm.clearStatus = undefined
  queryForm.timezone = undefined
  queryForm.bmType = undefined
  queryForm.recycleTime = undefined
  queryForm.refunded = undefined
  queryForm.tags = undefined
  queryForm.isOpen = undefined
  queryForm.campaignTime = undefined
  queryForm.channelName = undefined
  queryForm.enablePrepay = undefined
  queryForm.isOneDollar = undefined
  queryForm.bmAuthTime = undefined
  search()
}

// 导出
const onExport = () => {
  useDownload(() => exportAdAccountOrder(queryForm))
}

const AdAccountOrderAddModalRef = ref<InstanceType<typeof AdAccountOrderAddModal>>()
// 新增
const onAdd = () => {
  AdAccountOrderAddModalRef.value?.onAdd()
}

const AdAccountOrderBatchAddModalRef = ref<InstanceType<typeof AdAccountOrderBatchAddModal>>()
// 新增
const onBatchAdd = () => {
  AdAccountOrderBatchAddModalRef.value?.onOpen()
}

// 修改
const onUpdate = (record: AdAccountOrderResp) => {
  AdAccountOrderAddModalRef.value?.onUpdate(record.id)
}
const AdAccountOrderUpdateRef = ref<InstanceType<typeof AdAccountOrderUpdateModal>>()
const AdAccountOrderImportDrawerRef = ref<InstanceType<typeof AdAccountOrderImportDrawer>>()

const onRecycle = async (record: AdAccountOrderResp) => {
  await recycleOrder(record.id)
  Message.success('回收成功')
  refresh()
}

const onCancelRecycle = async (record: AdAccountOrderResp) => {
  await cancelRecycleOrder(record.id)
  Message.success('取消回收成功')
  refresh()
}

const onResetClearStatus = async (record: AdAccountOrderResp) => {
  await resetAdAccountOrderClearStatus(record.id)
  Message.success('取消清零成功')
  refresh()
}

const onAuthFail = async (record: AdAccountOrderResp) => {
  await authFailAdAccountOrder(record.id)
  Message.success('操作成功')
  refresh()
}

const onReceiveFail = async (record: AdAccountOrderResp) => {
  await receiveFailAdAccountOrder(record.id)
  Message.success('操作成功')
  refresh()
}

const onInvalid = async (record: AdAccountOrderResp) => {
  await invalidAdAccountOrder(record.id)
  Message.success('操作成功')
  refresh()
}

const onAuthSuccess = async (record: AdAccountOrderResp) => {
  await authSuccessAdAccountOrder(record.id)
  Message.success('操作成功')
  refresh()
}

const onHandle = async (id: string) => {
  await handleAdAccountOrder(id)
  Message.success('接单成功')
  refresh()
}

const onCancel = async (id: string) => {
  await cancelAdAccountOrder(id)
  Message.success('取消成功')
  refresh()
}

const onUpdateCustomerBmId = () => {
  AdAccountOrderUpdateRef.value?.onOpen(selectedKeys.value)
}
const userList = ref<LabelValueState[]>([])

const getUserList = async () => {
  const { data } = await listUserDict()
  userList.value = data
}
getUserList()

const AdAccountOrderTimezoneStatModalRef = ref<InstanceType<typeof AdAccountOrderTimezoneStatModal>>()
const onTimezoneStatOpen = () => {
  AdAccountOrderTimezoneStatModalRef.value?.onOpen()
}

const onCopySpent = async () => {
  let text = ''
  dataList.value.forEach((item) => {
    if (selectedKeys.value.includes(item.id)) {
      text += `${item.customerName}\t${item.adAccountId}\t消耗${item.cardSpent}\n`
    }
  })
  try {
    await navigator.clipboard.writeText(text)
    Message.success('复制成功')
  } catch (err) {
    Message.error('复制失败')
  }
}

const AdAccountDailyStatModalRef = ref<InstanceType<typeof AdAccountDailyStatModal>>()
const onDailyStatOpen = (id: string) => {
  AdAccountDailyStatModalRef.value?.onOpen(id)
}
const onSorterChange = (dataIndex: string, direction: string) => {
  let defaultSort = ['id,desc']
  if (direction) {
    if (dataIndex === 'customerName') {
      defaultSort = ['customer_id', direction === 'descend' ? 'desc' : 'asc']
    } else {
      defaultSort = [dataIndex, direction === 'descend' ? 'desc' : 'asc']
    }
  }
  queryForm.sort = defaultSort
  search()
}

// 退款
const onRefund = async (record: AdAccountOrderResp) => {
  await refundAdAccountOrder(record.id)
  Message.success('退款成功')
  refresh()
}

const AdAccountUpdateBM1ModalRef = ref<InstanceType<typeof AdAccountUpdateBM1Modal>>()
const onUpdateAdAccountBm1 = () => {
  const platformAdIds: string[] = []
  dataList.value.forEach((item) => {
    if (selectedKeys.value.includes(item.id)) {
      platformAdIds.push(item.adAccountId)
    }
  })
  AdAccountUpdateBM1ModalRef.value?.onOpen(platformAdIds.join('\n'))
}
const tagList = ref<LabelValueState[]>([])
const getTagList = async () => {
  const { data } = await listTagDict()
  tagList.value = data
}
getTagList()

const tagRefresh = async () => {
  await getTagList()
  refresh()
}

const AdAccountOrderOperationUserStatModalRef = ref<InstanceType<typeof AdAccountOrderOperationUserStatModal>>()
const onStatisticsOpen = () => {
  AdAccountOrderOperationUserStatModalRef.value?.onOpen()
}

const AdAccountOrderStatModalRef = ref<InstanceType<typeof AdAccountOrderStatModal>>()
const onOrderStatisticsOpen = () => {
  AdAccountOrderStatModalRef.value?.onOpen()
}
const BM5ChannelList = ref<any[]>([])
const BM5ChannelOption = reactive<Array<{ value: string | number, label: string }>>([])
const getBM5Channel = async () => {
  const res = await getBusinessManagerChannelList()
  BM5ChannelList.value = res.data
  // 使用 map 构建 option
  const newOptions = BM5ChannelList.value.map((item) => ({
    value: item.name,
    label: item.name,
  }))

  // 更新 option
  BM5ChannelOption.length = 0
  BM5ChannelOption.push(...newOptions)
}
getBM5Channel()
</script>

<style scoped lang="scss"></style>
