<template>
  <div class="table-page">
    <a-alert>
      统计近N天卡台总消耗&lt;T，在M天前下的正常户（可用状态的账户）。<br>通过BM授权时间判断新户和老户，如BM授权时间在一个月内的定义为新户
    </a-alert>
    <a-divider />
    <GiTable
      row-key="adAccountId"
      :data="dataList"
      size="medium"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
      :pagination="pagination"
      :disabled-tools="['size']"
      @reset="reset"
      @search="search"
      @sorter-change="onSorterChange"
      @refresh="search"
    >
      <template #toolbar-left>
        近
        <a-input-number
          v-model="queryForm.day"
          :min="1"
          placeholder="统计天数"
          hide-button
          style="width: 60px"
          @change="search"
        >
        </a-input-number>
        天卡台总消耗&lt;
        <a-input-number
          v-model="queryForm.spentLimit"
          :min="0"
          placeholder="消耗金额"
          hide-button
          style="width: 60px; margin-left: 2px"
          @change="search"
        />,
        <a-input-number
          v-model="queryForm.orderDaysBefore"
          :min="1"
          placeholder="下户天数"
          hide-button
          style="width: 60px"
          @change="search"
        >
        </a-input-number>
        天前下户
        <a-select
          v-model="queryForm.customerId"
          :options="customerList"
          placeholder="请选择客户"
          allow-clear
          allow-search
          style="width: 150px"
          @change="search"
        />
        <AdAccountTypeSelect v-model="queryForm.bmItemType" @change="search"></AdAccountTypeSelect>
        <a-select
          v-model="queryForm.timezone"
          :options="timezone_type"
          placeholder="请选择时区"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <DateRangePicker
          v-model="queryForm.bmAuthTime"
          :placeholder="['BM授权开始时间', 'BM授权结束时间']"
          :show-time="false"
          format="YYYY-MM-DD"
          @change="search"
        />
        <a-input-search
          v-model="queryForm.platformAdId"
          placeholder="请输入广告户ID"
          allow-clear
          @search="search"
        />
        <a-select
          v-model="queryForm.clearStatus"
          :options="ad_account_clear_status"
          placeholder="请选择清零状态"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <TrueAndFalseSelect v-model="queryForm.isStartCampaign" placeholder="是否开启广告系列" @change="search" />
      </template>
      <template #toolbar-right>
        <a-button @click="onExport">
          <template #icon><icon-download /></template>
          <template #default>导出</template>
        </a-button>
      </template>
      <template #browserNo="{ record }">
        <AdsBrowser :open-url="getAdAccountPaymentSettingMainPage(record.adAccountId)" :browser-no="record.browserNo"></AdsBrowser>
      </template>
      <template #cardSpent="{ record }">
        <a-space direction="vertical" size="mini">
          <div>{{ record.cardSpent }}</div>
          <a-tag v-if="record.campaignDesc" color="red">{{ record.campaignDesc }}</a-tag>
        </a-space>
      </template>
      <template #bmType="{ record }">
        <GiCellTag :value="record.bmType" :dict="business_manager_type" />
      </template>
      <template #clearStatus="{ record }">
        <GiCellTag :value="record.clearStatus" :dict="ad_account_clear_status" />
      </template>

      <template #action="{ record }">
        <a-space>
          <a-link v-permission="['biz:adAccount:update']" title="标记不可用" @click="onMarkUnusable(record)">标记不可用</a-link>
        </a-space>
      </template>

      <template #totalSpent="{ record }">
        <a-button type="text" @click="onDailyStatOpen(record.adAccountId)">{{ record.totalSpent }}</a-button>
      </template>
    </GiTable>

    <AdAccountDailyStatModal ref="AdAccountDailyStatModalRef" />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { Message, Modal, Select } from '@arco-design/web-vue'
import type { TableInstanceColumns } from '@/components/GiTable/type'
import { useDownload, useTable } from '@/hooks'
import { getAdAccountPaymentSettingMainPage, isMobile } from '@/utils'
import { useCustomer, useDict } from '@/hooks/app'
import AdsBrowser from '@/components/AdsBrowser/index.vue'
import AdAccountDailyStatModal from '@/views/biz/finance/adAccountDailyStatModal.vue'
import DateRangePicker from '@/components/DateRangePicker/index.vue'
import { exportUnspentAdAccountOrder, listUnspentAdAccountOrder } from '@/apis/biz/adAccountOrder'
import { updateAdAccountUnusableReason } from '@/apis/biz/adAccount'

defineOptions({ name: 'UnspentAdAccount' })

const {
  business_manager_type,
  ad_account_clear_status,
  timezone_type,
  ad_account_unusable_reason,
} = useDict(
  'business_manager_type',
  'ad_account_clear_status',
  'timezone_type',
  'ad_account_unusable_reason',
)
const { customerList, getCustomerList } = useCustomer()

// 查询表单
const queryForm = reactive({
  customerId: undefined,
  platformAdId: undefined,
  clearStatus: undefined,
  timezone: undefined,
  bmItemType: undefined, // 新增：账号类型筛选
  bmAuthTime: undefined, // 新增：BM授权时间筛选
  day: 7, // 统计天数
  spentLimit: 10, // 新增：消耗金额限制，默认10
  orderDaysBefore: 7, // 新增：下户天数，默认7天前
  isStartCampaign: undefined,
  sort: ['finishTime', 'desc'],
})

// 使用 useTable hook 处理表格数据
const {
  tableData: dataList,
  loading,
  pagination,
  search,
} = useTable((page) => listUnspentAdAccountOrder({ ...queryForm, ...page }), { immediate: false })

// 表格列定义
const columns = ref<TableInstanceColumns[]>([
  {
    title: '序号',
    width: 66,
    align: 'center',
    render: ({ rowIndex }) => h('span', {}, rowIndex + 1 + (pagination.current - 1) * pagination.pageSize),
    fixed: !isMobile() ? 'left' : undefined,
  },
  { title: '客户', dataIndex: 'customerName', slotName: 'customerName', width: 100 },
  { title: '账户类型', dataIndex: 'bmType', slotName: 'bmType', width: 180 },
  { title: '广告户ID', dataIndex: 'adAccountId', slotName: 'adAccountId', width: 180 },
  { title: '面板消耗', dataIndex: 'amountSpent', slotName: 'amountSpent', width: 100, align: 'center', sortable: {
    sortDirections: ['ascend', 'descend'],
    sorter: true,
  } },
  { title: '总消耗', dataIndex: 'totalSpent', slotName: 'totalSpent', width: 100, align: 'center' },
  { title: '卡台总消耗', dataIndex: 'cardSpent', slotName: 'cardSpent', width: 180, align: 'center' },
  { title: '浏览器', dataIndex: 'browserNo', slotName: 'browserNo', width: 100, align: 'center' },
  { title: '时区', dataIndex: 'timezone', slotName: 'timezone', width: 120 },
  { title: '清零状态', slotName: 'clearStatus', width: 120, align: 'center' },
  { title: '清零时间', slotName: 'clearTime', dataIndex: 'clearTime', width: 180, align: 'center' },
  { title: 'BM授权时间', slotName: 'bmAuthTime', dataIndex: 'bmAuthTime', width: 180, align: 'center' },
  { title: '下户时间', dataIndex: 'finishTime', width: 180, sortable: {
    sortDirections: ['ascend', 'descend'],
    sorter: true,
    defaultSortOrder: 'descend',
  } },
  { title: '最新充值金额', dataIndex: 'rechargeAmount', slotName: 'rechargeAmount', width: 100, align: 'center' },
  { title: '充值时间', dataIndex: 'rechargeTime', slotName: 'rechargeTime', width: 180, align: 'center' },
  { title: '最新消耗', dataIndex: 'spentAmount', slotName: 'spentAmount', width: 100, align: 'center' },
  { title: '消耗时间', dataIndex: 'spentTime', slotName: 'spentTime', width: 180, align: 'center' },
  {
    title: '操作',
    dataIndex: 'action',
    slotName: 'action',
    width: 200,
    align: 'center',
    fixed: !isMobile() ? 'right' : undefined,
  },
])

const onSorterChange = (dataIndex: string, direction: string) => {
  let defaultSort = ['finishTime,desc']
  if (direction) {
    defaultSort = [dataIndex, direction === 'descend' ? 'desc' : 'asc']
  }
  queryForm.sort = defaultSort
  search()
}

// 重置查询
const reset = () => {
  queryForm.customerId = undefined
  queryForm.platformAdId = undefined
  queryForm.clearStatus = undefined
  queryForm.timezone = undefined
  queryForm.bmItemType = undefined
  queryForm.bmAuthTime = undefined
  queryForm.spentLimit = 10
  queryForm.orderDaysBefore = 7
  search()
}

const AdAccountDailyStatModalRef = ref<InstanceType<typeof AdAccountDailyStatModal>>()
const onDailyStatOpen = (id: string) => {
  AdAccountDailyStatModalRef.value?.onOpen(id)
}

const onOpen = async () => {
  dataList.value = []
  await getCustomerList()
  search()
}

// 导出
const onExport = () => {
  useDownload(() => exportUnspentAdAccountOrder(queryForm))
}

const onMarkUnusable = (record: any) => {
  let selectedReason = null

  Modal.open({
    title: '标记不可用',
    content: () => h(Select, {
      options: ad_account_unusable_reason,
      placeholder: '请选择不可用原因',
      style: { width: '100%' },
      required: true,
      onChange: (value) => {
        selectedReason = value
      },
    }),
    onBeforeOk: async () => {
      if (!selectedReason) {
        Message.error('请选择不可用原因')
        return false
      }
      return true
    },
    onOk: async () => {
      try {
        await updateAdAccountUnusableReason({
          ids: [record.adAccountPrimaryKey],
          unusableReason: selectedReason,
        })
        Message.success('操作成功')
        search()
      } catch (error) {
        Message.error('操作失败')
        console.error(error)
      }
    },
  })
}

defineExpose({ onOpen })
</script>
