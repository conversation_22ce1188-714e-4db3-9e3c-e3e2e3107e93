<template>
  <a-modal
    v-model:visible="visible"
    title="下户订单统计"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 1200 ? 1200 : '100%'"
    draggable
    :footer="false"
  >
    <div class="table-page">
      <GiTable
        row-key="id"
        :data="dataList"
        :columns="columns"
        :loading="loading"
        :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
        :pagination="false"
        :disabled-tools="['size']"
        @refresh="search"
      >
        <template #toolbar-left>
          <CustomerSelect v-model="queryForm.customerId" @change="search" />
          <AdAccountTypeSelect v-model="queryForm.bmType" @change="search"></AdAccountTypeSelect>
          <a-select
            v-model="queryForm.bmChannelId"
            placeholder="请选择BM渠道"
            allow-clear
            allow-search
            style="width: 150px"
            @change="search"
          >
            <a-option
              v-for="item in BM5ChannelList"
              :key="item.id"
              :value="item.id"
              :label="item.name"
            />
          </a-select>
          <a-select
            v-model="queryForm.timezone"
            :options="timezone_type"
            placeholder="请选择时区"
            allow-clear
            style="width: 150px"
            @change="search"
          />
          <DateRangePicker v-model="queryForm.payTime" :show-time="false" :placeholder="['下户时间', '下户时间']" @change="search" />
          <a-button @click="reset">
            <template #icon>
              <icon-refresh />
            </template>
            <template #default>重置</template>
          </a-button>
        </template>
      </GiTable>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { useWindowSize } from '@vueuse/core/index'
import { useTable } from '@/hooks'
import { getAdAccountOrderStatistics } from '@/apis/biz/adAccountOrder'
import { getBusinessManagerChannelList } from '@/apis/biz/businessManagerChannel'
import type { TableInstanceColumns } from '@/components/GiTable/type'
import { useDict } from '@/hooks/app'

const { width } = useWindowSize()
const { timezone_type } = useDict( 'timezone_type')

const visible = ref(false)

const queryForm = reactive({
  customerId: undefined,
  bmType: undefined,
  bmChannelId: undefined,
  timezone: undefined,
  payTime: undefined,
})

const {
  tableData: dataList,
  loading,
  search,
} = useTable(() => getAdAccountOrderStatistics(queryForm), { immediate: false })

const columns = ref<TableInstanceColumns[]>([
  { title: '总下户数', dataIndex: 'totalOrderCount', align: 'center', width: 120 },
  { title: '正常数', dataIndex: 'normalCount', align: 'center', width: 120 },
  { title: '正常率', dataIndex: 'normalRate', align: 'center', width: 120, render: ({ record }) => `${(record.normalRate * 100).toFixed(2)}%` },
  { title: '挂户数', dataIndex: 'suspendedCount', align: 'center', width: 120 },
  { title: '挂户率', dataIndex: 'suspendedRate', align: 'center', width: 120, render: ({ record }) => `${(record.suspendedRate * 100).toFixed(2)}%` },
  { title: '未开启广告户数', dataIndex: 'notStartedCount', align: 'center', width: 150 },
  { title: '不消耗户数', dataIndex: 'noSpendCount', align: 'center', width: 120 },
  { title: '不消耗户率', dataIndex: 'noSpendRate', align: 'center', width: 120, render: ({ record }) => `${(record.noSpendRate * 100).toFixed(2)}%` },
])

const BM5ChannelList = ref<any[]>([])
const getBM5Channel = async () => {
  const res = await getBusinessManagerChannelList()
  BM5ChannelList.value = res.data
}

const reset = () => {
  queryForm.customerId = undefined
  queryForm.bmType = undefined
  queryForm.bmChannelId = undefined
  queryForm.timezone = undefined
  queryForm.payTime = undefined
  search()
}

// 打开弹框
const onOpen = async () => {
  await getBM5Channel()
  reset()
  visible.value = true
}

defineExpose({ onOpen })
</script>

<style scoped lang="scss">

</style>