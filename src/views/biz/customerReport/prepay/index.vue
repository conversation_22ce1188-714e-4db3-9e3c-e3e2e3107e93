<template>
  <div class="table-page">
    <div>
      <a-grid :cols="24" :col-gap="12" :row-gap="12">
        <a-grid-item v-for="(item, index) in summaryDataList" :key="index" :span="{ xs: 24, sm: 24, md: 24, lg: 6, xl: 4, xxl: 3 }">
          <a-card
            class="general-card"
          >
            <div class="content-wrap">
              <div class="content">
                <a-statistic
                  :title="item.name"
                  :value="item.value"
                  :value-from="0"
                  :precision="2"
                  animation
                  show-group-separator
                />
              </div>
            </div>
          </a-card>
        </a-grid-item>
      </a-grid>
    </div>
    <GiTable
      title="预付客户报告"
      row-key="id"
      :data="dataList"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
      :pagination="pagination"
      :disabled-tools="['size']"
      :disabled-column-keys="['name']"
      @refresh="search"
      @sorter-change="onSorterChange"
    >
      <template #toolbar-left>
        <a-select
          v-model="queryForm.customerId"
          placeholder="请选择客户"
          :options="customerList"
          allow-clear
          allow-search
          style="width: 150px"
          @change="customSearch"
        />
        <DateRangePicker v-model="queryForm.transTime" :show-time="false" @change="customSearch" />
        <a-button @click="reset">
          <template #icon><icon-refresh /></template>
          <template #default>重置</template>
        </a-button>
      </template>
      <template #toolbar-right>
        <a-button @click="onDailyStatOpen">
          <template #icon><icon-bar-chart /></template>
          <template #default>每日数据</template>
        </a-button>
        <a-button @click="onExport">
          <template #icon><icon-download /></template>
          <template #default>导出</template>
        </a-button>
      </template>
      <template #action="{ record }">
        <a-space>
          <a-link title="每日数据" @click="onDaily(record.customerId)">每日数据</a-link>
          <a-link title="导出日报" @click="onDailyExport(record.customerId)">导出日报</a-link>
        </a-space>
      </template>
    </GiTable>
    <DailyStatModal ref="DailyStatModalRef"></DailyStatModal>
    <CustomerDailyStatModal ref="customerDailyStatModalModal"></CustomerDailyStatModal>
  </div>
</template>

<script setup lang="ts">
import type { TableInstanceColumns } from '@/components/GiTable/type'
import { useDownload, useTable } from '@/hooks'
import { isMobile } from '@/utils'
import {
  type CustomerStatReportQuery,
  type CustomerStatSummaryResp,
  exportCustomerStatReport,
  getCustomerStatReportSummary,
  listCustomerStatReport,
} from '@/apis/biz/finance'
import { useCustomer } from '@/hooks/app/useCustomer'
import { exportCustomerDailyExcel } from '@/apis/biz/customer'
import DailyStatModal from '@/views/biz/customerReport/components/DailyStatModal.vue'
import type { DashboardChartCommonResp } from '@/apis'
import CustomerDailyStatModal from '@/views/biz/customerReport/components/customerDailyStatModal.vue'

defineOptions({ name: 'PostpayCustomerStat' })

const { customerList, getCustomerList } = useCustomer()

const summaryDataList = ref<DashboardChartCommonResp[]>([])

const queryForm = reactive<CustomerStatReportQuery>({
  customerId: undefined,
  isSelf: false,
  transTime: [],
  ascSortFlag: false,
  settleType: 1,
  businessType: 1,
  sortField: undefined,
})

const summaryData = ref<CustomerStatSummaryResp>({
  transferAmount: 0,
  adAccountRechargeAmount: 0,
  fee: 0,
  withdrawAmount: 0,
  totalRecharge: 0,
  adAccountBuyAmount: 0,
  cardSpent: 0,
  totalBalance: 0,
})

const {
  tableData: dataList,
  loading,
  pagination,
  search,
} = useTable((page) => listCustomerStatReport({ ...queryForm, ...page }), { immediate: true })
const columns = ref<TableInstanceColumns[]>([
  { title: '客户', dataIndex: 'customerName', slotName: 'customerName', width: 120 },
  { title: '当前余额', dataIndex: 'balance', slotName: 'balance', width: 120, align: 'center', sortable: {
    sortDirections: ['ascend', 'descend'],
    sorter: true,
  } },
  { title: '打款金额', dataIndex: 'transferAmount', slotName: 'transferAmount', width: 140, align: 'center', sortable: {
    sortDirections: ['ascend', 'descend'],
    sorter: true,
  } },
  { title: '退款金额', dataIndex: 'refundAmount', slotName: 'refundAmount', width: 140, align: 'center', sortable: {
    sortDirections: ['ascend', 'descend'],
    sorter: true,
  } },
  { title: '服务费', dataIndex: 'fee', slotName: 'fee', width: 120, align: 'center', sortable: {
    sortDirections: ['ascend', 'descend'],
    sorter: true,
  } },
  { title: '开户费', dataIndex: 'adAccountBuyAmount', slotName: 'adAccountBuyAmount', width: 120, align: 'center', sortable: {
    sortDirections: ['ascend', 'descend'],
    sorter: true,
  } },
  { title: '到账金额', dataIndex: 'totalRecharge', slotName: 'totalRecharge', width: 140, align: 'center', sortable: {
    sortDirections: ['ascend', 'descend'],
    sorter: true,
  } },
  { title: '取款金额', dataIndex: 'withdrawAmount', slotName: 'withdrawAmount', width: 120, align: 'center', sortable: {
    sortDirections: ['ascend', 'descend'],
    sorter: true,
  } },
  { title: '广告户充值金额', dataIndex: 'adAccountRechargeAmount', slotName: 'adAccountRechargeAmount', width: 160, align: 'center', sortable: {
    sortDirections: ['ascend', 'descend'],
    sorter: true,
  } },
  { title: '卡台消耗', dataIndex: 'cardSpent', slotName: 'cardSpent', width: 140, align: 'center', sortable: {
    sortDirections: ['ascend', 'descend'],
    sorter: true,
  } },
  {
    title: '操作',
    dataIndex: 'action',
    slotName: 'action',
    width: 200,
    align: 'center',
    fixed: !isMobile() ? 'right' : undefined,
  },
])

// 重置
const reset = () => {
  queryForm.customerId = undefined
  queryForm.transTime = []
  queryForm.sortField = undefined
  queryForm.ascSortFlag = false
  search()
}

// 导出
const onExport = () => {
  useDownload(() => exportCustomerStatReport(queryForm))
}

// 导出日报
const onDailyExport = (id: string) => {
  useDownload(() => exportCustomerDailyExcel(id))
}

const DailyStatModalRef = ref<InstanceType<typeof DailyStatModal>>()
const onDailyStatOpen = () => {
  DailyStatModalRef.value?.onOpen()
}

const onSorterChange = (dataIndex: string, direction: string) => {
  if (direction) {
    queryForm.sortField = dataIndex
    queryForm.ascSortFlag = direction !== 'descend'
  } else {
    queryForm.sortField = ''
    queryForm.ascSortFlag = undefined
  }
  search()
}

const customerDailyStatModalModal = ref<InstanceType<typeof CustomerDailyStatModal>>()

const onDaily = (id: string) => {
  customerDailyStatModalModal.value?.onOpen(id, queryForm.transTime)
}

const getSummaryData = async () => {
  const { data } = await getCustomerStatReportSummary(queryForm)
  summaryDataList.value = []
  summaryDataList.value.push({ name: '客户余额', value: data.totalBalance })
  summaryDataList.value.push({ name: '打款金额', value: data.transferAmount })
  summaryDataList.value.push({ name: '退款金额', value: data.refundAmount })
  summaryDataList.value.push({ name: '服务费', value: data.fee })
  summaryDataList.value.push({ name: '开户费', value: data.adAccountBuyAmount })
  summaryDataList.value.push({ name: '到账金额', value: data.totalRecharge })
  summaryDataList.value.push({ name: '取款金额', value: data.withdrawAmount })
  summaryDataList.value.push({ name: '广告户充值金额', value: data.adAccountRechargeAmount })
  // summaryDataList.value.push({ name: '卡台消耗', value: data.cardSpent })
  summaryData.value = data
}

const customSearch = () => {
  search()
  getSummaryData()
}

getCustomerList({ type: 1, isSelfAccount: false, settleType: 1 })
getSummaryData()
</script>

<style scoped lang="scss"></style>
