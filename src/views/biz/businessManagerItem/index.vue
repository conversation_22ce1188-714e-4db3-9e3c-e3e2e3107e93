<template>
  <div class="table-page">
    <GiTable
      title="bm坑位管理"
      row-key="id"
      :data="dataList"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
      :pagination="pagination"
      :disabled-tools="['size']"
      :disabled-column-keys="['name']"
      @refresh="search"
    >
      <template #toolbar-left>
        <a-select
          v-model="queryForm.businessManagerChannelId"
          :options="BMChannelList"
          placeholder="请选择坑位渠道"
          allow-clear
          allow-search
          style="width: 150px"
          @change="search"
        />
        <a-input-search v-model="queryForm.businessManager" placeholder="请输入BM ID" allow-clear @search="search" />
        <a-select
          v-model="queryForm.type"
          :options="profitTypeList"
          placeholder="请选择BM类型"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <a-select
          v-model="queryForm.status"
          :options="business_manager_status"
          placeholder="请选择状态"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <a-input-search v-model="queryForm.platformAdId" placeholder="请输入广告户ID" allow-clear @search="search" />
        <a-select
          v-model="queryForm.isUse"
          placeholder="是否使用"
          allow-clear
          style="width: 150px"
          @change="search"
        >
          <a-option label="是" :value="true"></a-option>
          <a-option label="否" :value="false"></a-option>
        </a-select>
        <a-select
          v-model="queryForm.isBu"
          placeholder="补号"
          allow-clear
          style="width: 150px"
          @change="search"
        >
          <a-option label="是" :value="true"></a-option>
          <a-option label="否" :value="false"></a-option>
        </a-select>
        <a-select
          v-model="queryForm.saleStatus"
          :options="ad_account_sale_status"
          placeholder="请选择出售状态"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <DateRangePicker v-model="queryForm.useTime" :placeholder="['使用时间', '使用时间']" :show-time="false" @change="search" />
        <DateRangePicker v-model="queryForm.banTime" :placeholder="['封禁时间', '封禁时间']" :show-time="false" @change="search" />
        <DateRangePicker v-model="queryForm.saleTime" :placeholder="['出售时间', '出售时间']" :show-time="false" @change="search" />
        <DateRangePicker v-model="queryForm.createTime" :placeholder="['创建时间', '创建时间']" :show-time="false" @change="search" />
        <a-button @click="reset">
          <template #icon><icon-refresh /></template>
          <template #default>重置</template>
        </a-button>
      </template>
      <template #toolbar-right>
        <a-button v-permission="['biz:businessManagerItem:export']" @click="onExport">
          <template #icon><icon-download /></template>
          <template #default>导出</template>
        </a-button>
      </template>
      <template #status="{ record }">
        <GiCellTag :value="record.status" :dict="business_manager_status" />
      </template>
      <template #saleStatus="{ record }">
        <GiCellTag :value="record.saleStatus" :dict="ad_account_sale_status" />
      </template>
      <template #isUse="{ record }">
        <a-tag v-if="record.isUse" color="arcoblue" size="small">是</a-tag>
        <a-tag v-else color="red" size="small">否</a-tag>
      </template>
      <template #ownMethod="{ record }">
        <a-tag v-if="record.ownMethod === 1" color="gray">认领</a-tag>
        <a-tag v-if="record.ownMethod === 2" color="gray">授权</a-tag>
      </template>
      <template #isBu="{ record }">
        <a-tag v-if="record.isBu" color="arcoblue" size="small">是</a-tag>
        <a-tag v-else color="red" size="small">否</a-tag>
      </template>
      <template #action="{ record }">
        <a-space>
          <a-link v-permission="['biz:businessManagerItem:update']" title="修改" @click="onUpdate(record)">修改</a-link>
          <a-link
            v-permission="['biz:businessManagerItem:delete']"
            status="danger"
            :disabled="record.disabled"
            :title="record.disabled ? '不可删除' : '删除'"
            @click="onDelete(record)"
          >
            删除
          </a-link>
        </a-space>
      </template>
    </GiTable>

    <BusinessManagerItemAddModal ref="BusinessManagerItemAddModalRef" @save-success="search" />
  </div>
</template>

<script setup lang="ts">
import BusinessManagerItemAddModal from './BusinessManagerItemAddModal.vue'
import { type BusinessManagerItemQuery, type BusinessManagerItemResp, deleteBusinessManagerItem, exportBusinessManagerItem, listBusinessManagerItem } from '@/apis/biz/businessManagerItem'
import type { TableInstanceColumns } from '@/components/GiTable/type'
import { useDownload, useTable } from '@/hooks'
import { isMobile } from '@/utils'
import has from '@/utils/has'
import { useDict } from '@/hooks/app'
import { listBusinessManagerChannelDict, listProfitTypeDict } from '@/apis'
import type { LabelValueState } from '@/types/global'

defineOptions({ name: 'BusinessManagerItem' })
const { business_manager_status, ad_account_sale_status } = useDict('business_manager_status', 'ad_account_sale_status')
const queryForm = reactive<BusinessManagerItemQuery>({
  businessManagerChannelId: undefined,
  businessManager: undefined,
  status: undefined,
  platformAdId: undefined,
  isUse: undefined,
  useTime: undefined,
  banTime: undefined,
  createTime: undefined,
  saleStatus: undefined,
  saleTime: undefined,
  type: undefined,
  isBu: undefined,
  sort: ['id,desc'],
})

const profitTypeList = ref<LabelValueState[]>([])

const getProfitTypeList = async () => {
  const { data } = await listProfitTypeDict({ adPlatform: '1', cat: 'ad_account' })
  profitTypeList.value = data
}

const {
  tableData: dataList,
  loading,
  pagination,
  search,
  handleDelete,
} = useTable((page) => listBusinessManagerItem({ ...queryForm, ...page }), { immediate: true })
const columns = ref<TableInstanceColumns[]>([
  {
    title: '序号',
    width: 66,
    align: 'center',
    render: ({ rowIndex }) => h('span', {}, rowIndex + 1 + (pagination.current - 1) * pagination.pageSize),
    fixed: !isMobile() ? 'left' : undefined,
  },
  { title: '坑位渠道', dataIndex: 'businessManagerChannel', slotName: 'businessManagerChannel', width: 120 },
  { title: 'BM ID', dataIndex: 'businessManager', slotName: 'businessManager', width: 180 },
  { title: 'BM类型', dataIndex: 'typeName', slotName: 'typeName', width: 120 },
  { title: '名称', dataIndex: 'name', slotName: 'name', width: 80 },
  { title: '单价', dataIndex: 'unitPrice', slotName: 'unitPrice', align: 'center', width: 80 },
  { title: '权限', dataIndex: 'ownMethod', slotName: 'ownMethod', align: 'center', width: 80 },
  { title: '状态', dataIndex: 'status', slotName: 'status', align: 'center', width: 80 },
  { title: '是否补号', dataIndex: 'isBu', slotName: 'isBu', align: 'center', width: 100 },
  { title: '出售状态', dataIndex: 'saleStatus', slotName: 'saleStatus', align: 'center', width: 100 },
  { title: '出售时间', dataIndex: 'saleTime', slotName: 'saleTime', align: 'center', width: 180 },
  { title: '广告户ID', dataIndex: 'platformAdId', slotName: 'platformAdId', width: 180 },
  { title: '是否使用', dataIndex: 'isUse', slotName: 'isUse', align: 'center', width: 100 },
  { title: '使用时间', dataIndex: 'useTime', slotName: 'useTime', width: 180 },
  { title: '封禁时间', dataIndex: 'banTime', slotName: 'banTime', width: 180 },
  { title: '备注', dataIndex: 'remark', slotName: 'remark', width: 100, ellipsis: true, tooltip: true },
  { title: '创建时间', dataIndex: 'createTime', slotName: 'createTime', width: 180 },
  {
    title: '操作',
    dataIndex: 'action',
    slotName: 'action',
    width: 160,
    align: 'center',
    fixed: !isMobile() ? 'right' : undefined,
    show: has.hasPermOr(['biz:businessManagerItem:detail', 'biz:businessManagerItem:update', 'biz:businessManagerItem:delete']),
  },
])

// 重置
const reset = () => {
  queryForm.businessManagerChannelId = undefined
  queryForm.businessManager = undefined
  queryForm.status = undefined
  queryForm.platformAdId = undefined
  queryForm.isUse = undefined
  queryForm.useTime = undefined
  queryForm.banTime = undefined
  queryForm.createTime = undefined
  search()
}

// 删除
const onDelete = (record: BusinessManagerItemResp) => {
  return handleDelete(() => deleteBusinessManagerItem(record.id), {
    content: `是否确定删除该条数据？`,
    showModal: true,
  })
}

// 导出
const onExport = () => {
  useDownload(() => exportBusinessManagerItem(queryForm))
}

const BusinessManagerItemAddModalRef = ref<InstanceType<typeof BusinessManagerItemAddModal>>()
// 新增
const onAdd = () => {
  BusinessManagerItemAddModalRef.value?.onAdd()
}

// 修改
const onUpdate = (record: BusinessManagerItemResp) => {
  BusinessManagerItemAddModalRef.value?.onUpdate(record.id)
}

const BMChannelList = ref<LabelValueState[]>([])

const getBMChannelList = async () => {
  const res = await listBusinessManagerChannelDict()
  BMChannelList.value = res.data
}
getBMChannelList()
getProfitTypeList()
</script>

<style scoped lang="scss"></style>
