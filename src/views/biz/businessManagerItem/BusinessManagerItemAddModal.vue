<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 600 ? 600 : '100%'"
    draggable
    @before-ok="save"
    @close="reset"
  >
    <GiForm ref="formRef" v-model="form" :options="options" :columns="columns" />
  </a-modal>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { Message } from '@arco-design/web-vue'
import { useWindowSize } from '@vueuse/core'
import { addBusinessManagerItem, getBusinessManagerItem, updateBusinessManagerItem } from '@/apis/biz/businessManagerItem'
import { type Columns, GiForm, type Options } from '@/components/GiForm'
import { useResetReactive } from '@/hooks'
import { useDict } from '@/hooks/app'
import { listBusinessManagerDict, listProfitTypeDict } from '@/apis'
import type { LabelValueState } from '@/types/global'
import { getBusinessManagerChannelList } from '@/apis/biz/businessManagerChannel'

const emit = defineEmits<{
  (e: 'save-success'): void
}>()

const { width } = useWindowSize()
const { business_manager_status } = useDict('business_manager_status')

const dataId = ref('')
const visible = ref(false)
const isUpdate = computed(() => !!dataId.value)
const title = computed(() => (isUpdate.value ? '修改bm坑位' : '新增bm坑位'))
const formRef = ref<InstanceType<typeof GiForm>>()
const toDay = ref('')

const options: Options = {
  form: { size: 'large' },
  btns: { hide: true },
}
const useOptions = [{
  label: '是',
  value: true,
}, {
  label: '否',
  value: false,
}]

const businessManagerOptions = ref<LabelValueState[]>([])

const [form, resetForm] = useResetReactive({
  // todo 待补充
  isUse: false,
  status: 1,
})

const profitTypeList = ref<LabelValueState[]>([])

const getProfitTypeList = async () => {
  const { data } = await listProfitTypeDict({ adPlatform: '1', cat: 'ad_account' })
  profitTypeList.value = data
}

const onBmSearch = async (value) => {
  if (value) {
    const { data } = await listBusinessManagerDict({ platformIds: value })
    businessManagerOptions.value = data
  } else {
    businessManagerOptions.value = []
  }
}
const option = reactive<Array<{ value: string | number, label: string }>>([])
const columns: Columns = reactive<Columns>([
  {
    label: 'BM账号',
    field: 'businessManagerId',
    type: 'select',
    options: businessManagerOptions,
    rules: [{ required: true, message: '请选择BM账号' }],
    props: {
      allowSearch: true,
      filterOption: false,
      showExtraOptions: false,
      disabled: isUpdate.value,
      onSearch: onBmSearch,
    },
    hide: () => isUpdate.value,
  },
  {
    label: '名称',
    field: 'name',
    type: 'input',
    rules: [{ required: true, message: '请输入名称' }],
  },
  {
    label: '单价',
    field: 'unitPrice',
    type: 'input-number',
    rules: [{ required: true, message: '请输入单价' }],
  },
  {
    label: '类型',
    field: 'type',
    type: 'select',
    options: profitTypeList,
    rules: [{ required: true, message: '请选择类型' }],
  },
  {
    label: '关联渠道',
    field: 'channelId',
    type: 'select',
    options: option,
    rules: [{ required: true, message: '请输入关联渠道' }],
    props: {
      allowSearch: true,
    },
  },
  {
    label: '权限',
    field: 'ownMethod',
    type: 'select',
    options: [
      { label: '认领', value: 1 },
      { label: '授权', value: 2 },
    ],
    rules: [{ required: true, message: '请选择权限' }],
    props: {
      allowSearch: true,
    },
  },
  {
    label: '状态',
    field: 'status',
    type: 'select',
    options: business_manager_status,
    hide: () => !isUpdate.value,
    rules: [{ required: true, message: '请选择状态' }],
  },
  {
    label: '封禁时间',
    field: 'banTime',
    type: 'date-picker',
    hide: () => form.status === 1,
    props: {
      showTime: true,
    },
  },
  {
    label: '广告户ID',
    field: 'platformAdId',
    type: 'input',
    hide: () => !isUpdate.value,
  },
  {
    label: '是否补号',
    field: 'isBu',
    type: 'radio-group',
    options: useOptions,
  },
  {
    label: '使用情况',
    field: 'isUse',
    type: 'radio-group',
    options: useOptions,
    hide: () => !isUpdate.value,
  },
  {
    label: '使用时间',
    field: 'useTime',
    type: 'date-picker',
    hide: () => !form.isUse,
    props: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      defaultValue: toDay,
    },
  },
  {
    label: '备注',
    field: 'remark',
    type: 'input',
  },
])

// 重置
const reset = () => {
  formRef.value?.formRef?.resetFields()
  resetForm()
  toDay.value = dayjs().format('YYYY-MM-DD HH:mm:ss')
}

// 保存
const save = async () => {
  try {
    const isInvalid = await formRef.value?.formRef?.validate()
    if (isInvalid) return false
    if (isUpdate.value) {
      await updateBusinessManagerItem(form, dataId.value)
      Message.success('修改成功')
    } else {
      await addBusinessManagerItem(form)
      Message.success('新增成功')
    }
    emit('save-success')
    return true
  } catch (error) {
    return false
  }
}

// 新增
const onAdd = async () => {
  reset()
  dataId.value = ''
  visible.value = true
}

// 修改
const onUpdate = async (id: string) => {
  reset()
  dataId.value = id
  const { data } = await getBusinessManagerItem(id)
  Object.assign(form, data)
  await getProfitTypeList()
  visible.value = true
}

const BM5ChannelList = ref<any[]>([])
const getBM5Channel = async () => {
  const res = await getBusinessManagerChannelList()
  BM5ChannelList.value = res.data
  // 使用 map 构建 option
  const newOptions = BM5ChannelList.value.map((item) => ({
    value: item.id,
    label: item.name,
  }))

  // 更新 option
  option.length = 0
  option.push(...newOptions)
}
onMounted(() => {
  getBM5Channel()
})

defineExpose({ onAdd, onUpdate })
</script>

<style scoped lang="scss"></style>
