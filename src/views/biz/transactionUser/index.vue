<template>
  <div class="table-page">
    <GiTable
      title="交易对象管理"
      row-key="id"
      :data="dataList"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
      :pagination="pagination"
      :disabled-tools="['size']"
      :disabled-column-keys="['name']"
      @refresh="search"
    >
      <template #toolbar-left>
        <a-select
          v-model="queryForm.transactionType"
          :options="transaction_type_enum"
          placeholder="请选择交易类型"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <a-input-search v-model="queryForm.name" placeholder="请输入名字" allow-clear @search="search" />
        <a-button @click="reset">
          <template #icon><icon-refresh /></template>
          <template #default>重置</template>
        </a-button>
      </template>
      <template #toolbar-right>
        <a-button v-permission="['biz:transactionUser:add']" type="primary" @click="onAdd">
          <template #icon><icon-plus /></template>
          <template #default>新增</template>
        </a-button>
        <a-button v-permission="['biz:transactionUser:export']" @click="onExport">
          <template #icon><icon-download /></template>
          <template #default>导出</template>
        </a-button>
      </template>
      <template #transactionType="{ record }">
        <GiCellTag :value="record.transactionType" :dict="transaction_user_type" />
      </template>
      <template #action="{ record }">
        <a-space>
          <a-link v-permission="['biz:transactionUser:update']" title="修改" @click="onUpdate(record)">修改</a-link>
          <a-link
            v-permission="['biz:transactionUser:delete']"
            status="danger"
            :disabled="record.disabled"
            :title="record.disabled ? '不可删除' : '删除'"
            @click="onDelete(record)"
          >
            删除
          </a-link>
        </a-space>
      </template>
    </GiTable>

    <TransactionUserAddModal ref="TransactionUserAddModalRef" @save-success="search" />
  </div>
</template>

<script setup lang="ts">
import TransactionUserAddModal from './TransactionUserAddModal.vue'
import { type TransactionUserQuery, type TransactionUserResp, deleteTransactionUser, exportTransactionUser, listTransactionUser } from '@/apis/biz/transactionUser'
import type { TableInstanceColumns } from '@/components/GiTable/type'
import { useDownload, useTable } from '@/hooks'
import { useDict } from '@/hooks/app'
import { isMobile } from '@/utils'
import has from '@/utils/has'

defineOptions({ name: 'TransactionUser' })

const { transaction_user_type } = useDict('transaction_user_type')

const queryForm = reactive<TransactionUserQuery>({
  transactionType: undefined,
  name: undefined,
  sort: ['id,desc'],
})

const {
  tableData: dataList,
  loading,
  pagination,
  search,
  handleDelete,
} = useTable((page) => listTransactionUser({ ...queryForm, ...page }), { immediate: true })
const columns = ref<TableInstanceColumns[]>([
  {
    title: '序号',
    width: 66,
    align: 'center',
    render: ({ rowIndex }) => h('span', {}, rowIndex + 1 + (pagination.current - 1) * pagination.pageSize),
    fixed: !isMobile() ? 'left' : undefined,
  },
  { title: '交易类型', dataIndex: 'transactionType', slotName: 'transactionType', align: 'center' },
  { title: '名字', dataIndex: 'name', slotName: 'name' },
  { title: '创建人', dataIndex: 'createUserString', slotName: 'createUser' },
  { title: '创建时间', dataIndex: 'createTime', slotName: 'createTime' },
  {
    title: '操作',
    dataIndex: 'action',
    slotName: 'action',
    width: 160,
    align: 'center',
    fixed: !isMobile() ? 'right' : undefined,
    show: has.hasPermOr(['biz:transactionUser:detail', 'biz:transactionUser:update', 'biz:transactionUser:delete']),
  },
])

// 重置
const reset = () => {
  queryForm.transactionType = undefined
  queryForm.name = undefined
  search()
}

// 删除
const onDelete = (record: TransactionUserResp) => {
  return handleDelete(() => deleteTransactionUser(record.id), {
    content: `是否确定删除该条数据？`,
    showModal: true,
  })
}

// 导出
const onExport = () => {
  useDownload(() => exportTransactionUser(queryForm))
}

const TransactionUserAddModalRef = ref<InstanceType<typeof TransactionUserAddModal>>()
// 新增
const onAdd = () => {
  TransactionUserAddModalRef.value?.onAdd()
}

// 修改
const onUpdate = (record: TransactionUserResp) => {
  TransactionUserAddModalRef.value?.onUpdate(record.id)
}
</script>

<style scoped lang="scss"></style>
