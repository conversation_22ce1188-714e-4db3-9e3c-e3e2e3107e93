<template>
  <a-drawer v-model:visible="visible" title="交易对象详情" :width="width >= 600 ? 600 : '100%'" :footer="false">
    <a-descriptions :column="2" size="large" class="general-description">
      <a-descriptions-item label="ID">{{ dataDetail?.id }}</a-descriptions-item>
      <a-descriptions-item label="交易类型">{{ dataDetail?.transactionType }}</a-descriptions-item>
      <a-descriptions-item label="关联ID">{{ dataDetail?.referId }}</a-descriptions-item>
      <a-descriptions-item label="名字">{{ dataDetail?.name }}</a-descriptions-item>
      <a-descriptions-item label="创建人">{{ dataDetail?.createUser }}</a-descriptions-item>
      <a-descriptions-item label="创建人">{{ dataDetail?.createUserString }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ dataDetail?.createTime }}</a-descriptions-item>
    </a-descriptions>
  </a-drawer>
</template>

<script setup lang="ts">
import { useWindowSize } from '@vueuse/core'
import { type TransactionUserDetailResp, getTransactionUser as getDetail } from '@/apis/biz/transactionUser'

const { width } = useWindowSize()

const dataId = ref('')
const dataDetail = ref<TransactionUserDetailResp>()
const visible = ref(false)

// 查询详情
const getDataDetail = async () => {
  const { data } = await getDetail(dataId.value)
  dataDetail.value = data
}

// 打开
const onOpen = async (id: string) => {
  dataId.value = id
  await getDataDetail()
  visible.value = true
}

defineExpose({ onOpen })
</script>

<style scoped lang="scss"></style>
