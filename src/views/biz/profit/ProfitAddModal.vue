<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 600 ? 600 : '100%'"
    draggable
    @before-ok="save"
    @close="reset"
  >
    <GiForm ref="formRef" v-model="form" :options="options" :columns="columns" />
  </a-modal>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import { useWindowSize } from '@vueuse/core'
import { addProfit, getProfit, updateProfit } from '@/apis/biz/profit'
import { type Columns, GiForm, type Options } from '@/components/GiForm'
import { useResetReactive } from '@/hooks'
import { useDict } from '@/hooks/app'
import type { LabelValueState } from '@/types/global'
import { listProfitTypeDict, listTransactionUserDict } from '@/apis'

const emit = defineEmits<{
  (e: 'save-success'): void
}>()

const { width } = useWindowSize()

const dataId = ref('')
const visible = ref(false)
const isUpdate = computed(() => !!dataId.value)
const title = computed(() => (isUpdate.value ? '修改利润' : '新增利润'))
const formRef = ref<InstanceType<typeof GiForm>>()
const { ad_project, ad_platform, transaction_user_type } = useDict('ad_project', 'ad_platform', 'transaction_user_type')

const profitTypeList = ref<LabelValueState[]>([])

const transactionUserList = ref<LabelValueState[]>([])

const options: Options = {
  form: { size: 'large' },
  btns: { hide: true },
}

const [form, resetForm] = useResetReactive({
  // todo 待补充
})

const getProfitTypeList = async () => {
  const { data } = await listProfitTypeDict({ adPlatform: form.adPlatform })
  profitTypeList.value = data
}

const onAdPlatformChange = () => {
  getProfitTypeList()
}

const getTransactionUserList = async () => {
  const { data } = await listTransactionUserDict({ transactionType: form.transactionUserType })
  transactionUserList.value = data
}

const onTransactionUserTypeChange = () => {
  getTransactionUserList()
}

const columns: Columns = reactive<Columns>([
  {
    label: '媒体平台',
    field: 'adPlatform',
    type: 'select',
    options: ad_platform,
    disabled: () => isUpdate.value,
    rules: [{ required: true, message: '请输入媒体平台' }],
    props: {
      allowClear: false,
      onChange: onAdPlatformChange,
    },
  },
  {
    label: '项目',
    field: 'project',
    type: 'select',
    options: ad_project,
    disabled: () => isUpdate.value,
    rules: [{ required: true, message: '请输入项目' }],
  },
  {
    label: '交易对象类型',
    field: 'transactionUserType',
    type: 'select',
    options: transaction_user_type,
    disabled: () => isUpdate.value,
    rules: [{ required: true, message: '请输入交易对象类型' }],
    props: {
      allowSearch: true,
      onChange: onTransactionUserTypeChange,
    },
  },
  {
    label: '交易对象',
    field: 'transactionUserId',
    type: 'select',
    options: transactionUserList,
    disabled: () => isUpdate.value,
    rules: [{ required: true, message: '请输入交易对象类型' }],
    props: {
      allowSearch: true,
    },
  },
  {
    label: '物料类型',
    field: 'type',
    type: 'select',
    options: profitTypeList,
    disabled: () => isUpdate.value,
    rules: [{ required: true, message: '请输入物料类型' }],
    props: {
      allowSearch: true,
    },
  },
  {
    label: '物料数量',
    field: 'num',
    type: 'input-number',
    disabled: () => isUpdate.value,
    rules: [{ required: true, message: '请输入物料数量' }],
    props: {
      min: 1,
    },
  },
  {
    label: '交易金额',
    field: 'amount',
    type: 'input-number',
    disabled: () => isUpdate.value,
    rules: [{ required: true, message: '请输入交易金额' }],
  },
  {
    label: '交易时间',
    field: 'transTime',
    type: 'date-picker',
    disabled: () => isUpdate.value,
    props: {
      showTime: true,
    },
    rules: [{ required: true, message: '请输入交易时间' }],
  },
  {
    label: '交易哈希',
    field: 'transactionHash',
    type: 'textarea',
    props: {
      autoSize: true,
    },
  },
  {
    label: '备注',
    field: 'remark',
    type: 'textarea',
    props: {
      autoSize: true,
    },
  },
])

// 重置
const reset = () => {
  formRef.value?.formRef?.resetFields()
  resetForm()
}

// 保存
const save = async () => {
  try {
    const isInvalid = await formRef.value?.formRef?.validate()
    if (isInvalid) return false
    if (isUpdate.value) {
      await updateProfit(form, dataId.value)
      Message.success('修改成功')
    } else {
      await addProfit(form)
      Message.success('新增成功')
    }
    emit('save-success')
    return true
  } catch (error) {
    return false
  }
}

// 新增
const onAdd = async () => {
  reset()
  dataId.value = ''
  visible.value = true
}

// 修改
const onUpdate = async (id: string) => {
  reset()
  dataId.value = id
  const { data } = await getProfit(id)
  Object.assign(form, data)
  await getProfitTypeList()
  visible.value = true
}

defineExpose({ onAdd, onUpdate })
</script>

<style scoped lang="scss"></style>
