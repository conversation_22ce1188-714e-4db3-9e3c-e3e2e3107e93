<template>
  <div class="table-page">
    <GiTable
      title="利润管理"
      row-key="id"
      :data="dataList"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
      :pagination="pagination"
      :disabled-tools="['size']"
      :disabled-column-keys="['name']"
      @refresh="search"
    >
      <template #toolbar-left>
        <a-select
          v-model="queryForm.adPlatform"
          :options="ad_platform"
          placeholder="请选择媒体平台"
          allow-clear
          style="width: 150px"
          @change="onAdPlatformChange"
        />
        <a-select
          v-model="queryForm.project"
          :options="ad_project"
          placeholder="请选择项目"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <a-select
          v-model="queryForm.type"
          :options="profitTypeList"
          placeholder="请选择类型"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <DateRangePicker v-model="queryForm.transTime" @change="search" />
        <a-input-search v-model="queryForm.transactionHash" placeholder="请输入交易哈希" allow-clear @search="search" />
        <a-input-search v-model="queryForm.remark" placeholder="请输入备注" allow-clear @search="search" />
        <a-button @click="reset">
          <template #icon><icon-refresh /></template>
          <template #default>重置</template>
        </a-button>
      </template>
      <template #toolbar-right>
        <a-button v-permission="['biz:profit:add']" type="primary" @click="onAdd">
          <template #icon><icon-plus /></template>
          <template #default>新增</template>
        </a-button>
        <a-button v-permission="['biz:profit:export']" @click="onExport">
          <template #icon><icon-download /></template>
          <template #default>导出</template>
        </a-button>
      </template>
      <template #adPlatform="{ record }">
        <GiCellTag :value="record.adPlatform" :dict="ad_platform" />
      </template>
      <template #project="{ record }">
        <GiCellTag :value="record.project" :dict="ad_project" />
      </template>
      <template #transactionUser="{ record }">
        <a-space size="mini" direction="vertical">
          <GiCellTag :value="record.transactionUserType" :dict="transaction_user_type" />
          <div>
            {{ record.transactionUserName }}
          </div>
        </a-space>
      </template>
      <template #action="{ record }">
        <a-space>
          <a-link v-permission="['biz:profit:update']" title="修改" @click="onUpdate(record)">修改</a-link>
          <a-link
            v-permission="['biz:profit:delete']"
            status="danger"
            :disabled="record.disabled"
            :title="record.disabled ? '不可删除' : '删除'"
            @click="onDelete(record)"
          >
            删除
          </a-link>
        </a-space>
      </template>
    </GiTable>

    <ProfitAddModal ref="ProfitAddModalRef" @save-success="search" />
  </div>
</template>

<script setup lang="ts">
import ProfitAddModal from './ProfitAddModal.vue'
import { type ProfitQuery, type ProfitResp, deleteProfit, exportProfit, listProfit } from '@/apis/biz/profit'
import type { TableInstanceColumns } from '@/components/GiTable/type'
import { useDownload, useTable } from '@/hooks'
import { useDict } from '@/hooks/app'
import { isMobile } from '@/utils'
import has from '@/utils/has'
import type { LabelValueState } from '@/types/global'
import { listProfitTypeDict } from '@/apis'

defineOptions({ name: 'Profit' })

const { ad_project, ad_platform, transaction_user_type } = useDict('ad_project', 'ad_platform', 'transaction_user_type')

const queryForm = reactive<ProfitQuery>({
  id: undefined,
  adPlatform: undefined,
  project: undefined,
  type: undefined,
  transTime: undefined,
  transactionHash: undefined,
  remark: undefined,
  sort: ['transTime,desc'],
})

const profitTypeList = ref<LabelValueState[]>([])

const getProfitTypeList = async () => {
  const { data } = await listProfitTypeDict({ adPlatform: queryForm.adPlatform })
  profitTypeList.value = data
}

const onAdPlatformChange = () => {
  queryForm.type = undefined
  profitTypeList.value = []
  if (queryForm.adPlatform) {
    getProfitTypeList()
  }
}

const {
  tableData: dataList,
  loading,
  pagination,
  search,
  handleDelete,
} = useTable((page) => listProfit({ ...queryForm, ...page }), { immediate: true })
const columns = ref<TableInstanceColumns[]>([
  {
    title: '序号',
    width: 66,
    align: 'center',
    render: ({ rowIndex }) => h('span', {}, rowIndex + 1 + (pagination.current - 1) * pagination.pageSize),
    fixed: !isMobile() ? 'left' : undefined,
  },
  { title: '媒体平台', dataIndex: 'adPlatform', slotName: 'adPlatform', align: 'center' },
  { title: '项目', dataIndex: 'project', slotName: 'project', align: 'center' },
  { title: '交易对象', dataIndex: 'transactionUser', slotName: 'transactionUser' },
  { title: '物料类型', dataIndex: 'typeName', slotName: 'typeName', align: 'center' },
  { title: '交易金额', dataIndex: 'amount', slotName: 'amount', align: 'center' },
  { title: '交易时间', dataIndex: 'transTime', slotName: 'transTime', width: 180 },
  { title: '交易哈希', dataIndex: 'transactionHash', slotName: 'transactionHash', width: 180, ellipsis: true, tooltip: true },
  { title: '备注', dataIndex: 'remark', slotName: 'remark', width: 180, ellipsis: true, tooltip: true },
  { title: '创建人', dataIndex: 'createUserString', slotName: 'createUser' },
  { title: '创建时间', dataIndex: 'createTime', slotName: 'createTime' },
  {
    title: '操作',
    dataIndex: 'action',
    slotName: 'action',
    width: 160,
    align: 'center',
    fixed: !isMobile() ? 'right' : undefined,
    show: has.hasPermOr(['biz:profit:detail', 'biz:profit:update', 'biz:profit:delete']),
  },
])

// 重置
const reset = () => {
  queryForm.id = undefined
  queryForm.adPlatform = undefined
  queryForm.project = undefined
  queryForm.type = undefined
  queryForm.transTime = undefined
  queryForm.transactionHash = undefined
  queryForm.remark = undefined
  search()
}

// 删除
const onDelete = (record: ProfitResp) => {
  return handleDelete(() => deleteProfit(record.id), {
    content: `是否确定删除该条数据？`,
    showModal: true,
  })
}

// 导出
const onExport = () => {
  useDownload(() => exportProfit(queryForm))
}

const ProfitAddModalRef = ref<InstanceType<typeof ProfitAddModal>>()
// 新增
const onAdd = () => {
  ProfitAddModalRef.value?.onAdd()
}

// 修改
const onUpdate = (record: ProfitResp) => {
  ProfitAddModalRef.value?.onUpdate(record.id)
}
</script>

<style scoped lang="scss"></style>
