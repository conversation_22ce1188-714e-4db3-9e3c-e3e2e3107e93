<template>
  <div class="table-page">
    <div class="daily-title">今日统计</div>
    <div class="statistic-container">
      <a-statistic
        v-for="(item, index) in dailyDataList"
        :key="index"
        :title="Object.keys(item)[0]"
        :value="Object.values(item)[0] as number"
      />
    </div>
    <GiTable
      v-model:selected-keys="selectedKeys"
      :row-selection="{
        type: 'checkbox',
        showCheckedAll: true,
        onlyCurrent: true,
      }"
      title="广告账号管理"
      row-key="id"
      columns-cache-key="adAccountList"
      :data="dataList"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
      :pagination="pagination"
      :disabled-tools="['size']"
      :disabled-column-keys="['name']"
      @select="select"
      @select-all="selectAll"
      @refresh="refresh"
      @sorter-change="onSorterChange"
    >
      <template #toolbar-left>
        <a-textarea
          v-if="!yanghaoMode"
          v-model="bmIdInput"
          placeholder="请输入BM ID，多个ID可用空格、逗号、换行符分隔"
          allow-clear
          :auto-size="{ minRows: 1, maxRows: 3 }"
          style="width: 300px"
          @change="handlePlatformIdsChange"
        />
        <AdAccountTypeSelect v-model="queryForm.bmItemType" multiple @change="search"></AdAccountTypeSelect>
        <a-input-search
          v-model="queryForm.tag" placeholder="请输入标签" allow-clear @search="search"
          @clear="queryForm.tag = undefined"
        />
        <a-textarea
          v-model="adAccountIdInput"
          placeholder="请输入广告户ID，多个ID可用空格、逗号、换行符分隔"
          allow-clear
          :auto-size="{ minRows: 1, maxRows: 3 }"
          style="width: 300px"
          @change="handleAdAccountIdsChange"
        />
        <a-select
          v-model="queryForm.channelName" placeholder="请选择渠道" allow-search allow-clear :options="BM5ChannelOption"
          style="width: 150px"
          @change="search"
        />
        <a-input-search v-if="!yanghaoMode" v-model="queryForm.fullCardNumber" placeholder="请输入完整卡号" allow-clear @search="search" />
        <a-input-search v-if="yanghaoMode" v-model="queryForm.browserNo" placeholder="大黑号浏览器" allow-clear @search="search" />
        <a-input-search v-model="queryForm.remark" placeholder="请输入备注" allow-clear @search="search" />
        <a-select
          v-if="yanghaoMode"
          v-model="queryForm.fbChannelId"
          :options="fbChannelList"
          placeholder="请选择个号渠道"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <a-select
          v-model="queryForm.timezone"
          :options="timezone_type"
          placeholder="请选择时区"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <a-select
          v-model="queryForm.accountStatus"
          :options="ad_account_status"
          placeholder="请选择账号状态"
          allow-clear
          multiple
          style="width: 150px"
          @change="search"
        />
        <a-select
          v-model="queryForm.keepStatus"
          :options="ad_account_keep_status"
          placeholder="请选择养号状态"
          allow-clear
          multiple
          style="width: 150px"
          @change="search"
        />
        <a-select
          v-model="queryForm.saleStatus"
          :options="ad_account_sale_status"
          placeholder="请选择出售状态"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <a-select
          v-if="!yanghaoMode"
          v-model="queryForm.clearStatus"
          :options="ad_account_clear_status"
          placeholder="请选择清零状态"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <a-select
          v-if="!yanghaoMode"
          v-model="queryForm.appealStatus"
          :options="ad_account_appeal_status"
          placeholder="请选择申诉状态"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <a-select
          v-if="!yanghaoMode"
          v-model="queryForm.isRemoveAdmin"
          placeholder="移除管理员"
          allow-clear
          style="width: 150px"
          @change="search"
        >
          <a-option label="是" :value="true"></a-option>
          <a-option label="否" :value="false"></a-option>
        </a-select>
        <a-select
          v-if="!yanghaoMode"
          v-model="queryForm.isLowLimit"
          placeholder="低限户"
          allow-clear
          style="width: 150px"
          @change="search"
        >
          <a-option label="是" :value="true"></a-option>
          <a-option label="否" :value="false"></a-option>
        </a-select>
        <a-select
          v-if="!yanghaoMode"
          v-model="queryForm.voStatus"
          :options="ad_account_vo_status"
          placeholder="请选择vo状态"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <a-tree-select
          v-if="yanghaoMode"
          v-model="queryForm.deptId"
          :data="deptList"
          placeholder="请选择所属部门"
          allow-clear
          allow-search
          :filter-tree-node="filterDeptOptions"
          @change="search"
        />
        <a-select
          v-model="queryForm.createUser"
          :options="userList"
          placeholder="请选择创建人"
          multiple
          allow-clear
          allow-search
          @change="search"
        >
        </a-select>
        <a-select
          v-if="!yanghaoMode"
          v-model="queryForm.bm1Id"
          :options="bm1List"
          placeholder="请选择BM1"
          allow-search
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <a-input-group v-if="yanghaoMode" style="width: 300px">
          <a-input v-model="queryForm.firstSpend" placeholder="消耗范围" allow-clear />
          <a-input-search v-model="queryForm.lastSpend" placeholder="消耗范围" allow-clear @search="search" />
        </a-input-group>
        <DateRangePicker
          v-model="opsTimeRange" format="YYYY-MM-DD" :placeholder="['创建时间', '创建时间']"
          @change="onOpsTimeChange"
        />
        <DateRangePicker
          v-model="queryForm.updateTime" :placeholder="['更新时间', '更新时间']"
          @change="search"
        />
        <DateRangePicker
          v-if="!yanghaoMode" v-model="queryForm.bmAuthTime" :placeholder="['bm授权时间', 'bm授权时间']"
          :show-time="false"
          @change="search"
        />
        <DateRangePicker
          v-if="!yanghaoMode" v-model="queryForm.saleTime" :placeholder="['出售时间', '出售时间']"
          :show-time="false"
          @change="search"
        />
        <DateRangePicker
          v-if="!yanghaoMode" v-model="queryForm.banTime" :placeholder="['封禁时间', '封禁时间']"
          :show-time="false"
          @change="search"
        />
        <a-select
          v-model="queryForm.tags"
          placeholder="请选择标签"
          allow-search
          allow-clear
          multiple
          style="width: 150px"
          @change="search"
        >
          <a-option
            v-for="item in tagList"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          />
        </a-select>
        <a-select
          v-model="queryForm.usable"
          placeholder="是否可用"
          allow-clear
          style="width: 150px"
          @change="search"
        >
          <a-option :value="true" label="可用"></a-option>
          <a-option :value="false" label="不可用"></a-option>
        </a-select>
        <a-select
          v-model="queryForm.unusableReason"
          :options="ad_account_unusable_reason"
          placeholder="请选择不可用原因"
          allow-clear
          multiple
          style="width: 150px"
          @change="search"
        />
        <a-button @click="reset">
          <template #icon>
            <icon-refresh />
          </template>
          <template #default>重置</template>
        </a-button>
      </template>
      <template #toolbar-right>
        <a-switch
          :default-checked="yanghaoMode" checked-text="养号模式" unchecked-text="普通模式" @change="(value) => {
            appStore.setAdAccountModel(value ? 2 : 1)
          }"
        ></a-switch>
        <a-button type="primary" status="warning" :disabled="selectedKeys.length === 0" @click="onBatchUpdate">
          批量修改
        </a-button>
        <a-button status="warning" type="primary" :disabled="selectedKeys.length === 0" @click="onBindCard()">
          批量绑卡
        </a-button>
        <a-button status="warning" type="primary" :disabled="selectedKeys.length === 0" @click="onOpenAddTestOrder()">
          新增测试任务
        </a-button>
        <a-button status="warning" type="primary" @click="onUpdateBm1()">
          修改BM1
        </a-button>
        <a-button v-if="yanghaoMode" status="warning" type="primary" :loading="checkAdAccountNurturingStatusLoading" @click="onCheckAdAccountNurturingStatus()">消耗检测</a-button>
        <a-dropdown v-if="yanghaoMode" :popup-max-height="false">
          <a-button>
            复制浏览器编号
            <icon-down />
          </a-button>
          <template #content>
            <a-doption @click="copyBrowser(1)">复制停用</a-doption>
            <a-doption :disabled="selectedKeys.length === 0" @click="copyBrowser(2)">复制所选</a-doption>
          </template>
        </a-dropdown>
        <a-button v-if="yanghaoMode" @click="onBrowserCheck">
          <template #icon>
            <icon-check />
          </template>
          <template #default>浏览器丢失检测</template>
        </a-button>
        <a-button :disabled="selectedKeys.length === 0" @click="browserLaunch()">一键启动</a-button>
        <a-button v-permission="['biz:adAccount:export']" @click="onExport">
          <template #icon>
            <icon-download />
          </template>
          <template #default>导出</template>
        </a-button>
      </template>
      <template #accountStatus="{ record }">
        <GiCellTag :value="record.accountStatus" :dict="ad_account_status" />
      </template>
      <template #bmItemType="{ record }">
        <GiCellTag :value="record.bmItemType" :dict="business_manager_type" />
      </template>
      <template #usableWithReason="{ record }">
        <div>
          <a-tag v-if="record.usable" color="green" size="small">可用</a-tag>
          <a-tag v-else color="red" size="small">不可用</a-tag>
          <template v-if="!record.usable && record.unusableReason">
            <GiCellTag :value="record.unusableReason" :dict="ad_account_unusable_reason" />
          </template>
        </div>
      </template>
      <template #keepStatus="{ record }">
        <GiCellTag :value="record.keepStatus" :dict="ad_account_keep_status" />
      </template>
      <template #adTag="{ record }">
        <a-space direction="vertical" wrap>
          <a-tag v-if="!record.parentBrowserNo && record.saleStatus === 2" color="red">未监测</a-tag>
          <a-tag v-if="!record.isRemoveAdmin" color="red">未移除管理员</a-tag>
          <a-tag v-if="record.isLowLimit" color="red">低限户</a-tag>
          <a-tag v-if="record.voStatus === 1" color="gray">vo未检测</a-tag>
          <a-tag v-else-if="record.voStatus === 2" color="green">带vo</a-tag>
          <a-tag v-else color="red">不带vo</a-tag>
          <a-tag v-if="!record.headers" color="red">需手动检测消耗</a-tag>
        </a-space>
      </template>
      <template #saleStatus="{ record }">
        <GiCellTag :value="record.saleStatus" :dict="ad_account_sale_status" />
      </template>
      <template #clearStatus="{ record }">
        <GiCellTag :value="record.clearStatus" :dict="ad_account_clear_status" />
      </template>
      <template #appealStatus="{ record }">
        <GiCellTag :value="record.appealStatus" :dict="ad_account_appeal_status" />
      </template>
      <template #cardCount="{ record }">
        <a-link @click="onCard(record.platformAdId)">{{ record.cardCount }}</a-link>
      </template>
      <template #testOrderCount="{ record }">
        <a-link @click="onOpenTestOrder(record.platformAdId)">{{ record.testOrderCount }}</a-link>
      </template>
      <template #bmBrowser="{ record }">
        <AdsBrowser
          :ref="getAdsBrowserRef(record)"
          :open-url="getAdAccountPaymentSettingMainPage(record.platformAdId)" :browser-no="record.bmBrowser"
          :status="0"
        ></AdsBrowser>
      </template>
      <template #amountSpent="{ record }">
        <a-link @click="onOpenSpent(record.platformAdId)">{{ record.amountSpent }}</a-link>
      </template>
      <template #tags="{ record }">
        <Tag :id="record.id" :value="record.tags" :data="tagList" :type="1" @refresh="tagRefresh" />
      </template>
      <template #action="{ record }">
        <a-space>
          <a-link v-permission="['biz:adAccount:update']" title="修改" @click="onUpdate(record)">修改</a-link>
          <a-link v-if="record.usable" v-permission="['biz:adAccount:update']" title="标记不可用" @click="onMarkUnusable(record)">标记不可用</a-link>
          <a-link v-else v-permission="['biz:adAccount:update']" title="恢复可用" @click="onRecoverUnable(record.platformAdId)">恢复可用</a-link>
          <a-link
            v-permission="['biz:adAccount:delete']"
            status="danger"
            :disabled="record.disabled"
            :title="record.disabled ? '不可删除' : '删除'"
            @click="onDelete(record)"
          >
            删除
          </a-link>
        </a-space>
      </template>
    </GiTable>

    <AdAccountAddModal ref="AdAccountAddModalRef" @save-success="refresh" />
    <AdAccountCardModal ref="AdAccountCardModalRef" />
    <AdAccountBrowserCheckModal ref="AdAccountBrowserCheckModalRef"></AdAccountBrowserCheckModal>
    <AdAccountBatchUpdateModal ref="AdAccountBatchUpdateModalRef" @success="refresh"></AdAccountBatchUpdateModal>
    <AdAccountBindCardModal ref="AdAccountBindCardModalRef"></AdAccountBindCardModal>
    <AdAccountSpentCheckModal ref="AdAccountSpentCheckModalRef" />
    <AdAccountUpdateBM1Modal ref="AdAccountUpdateBM1ModalRef" @save-success="refresh" />
    <AdAccountSpentModal ref="AdAccountSpentModalRef" />
    <AdAccountTestOrderModal ref="AdAccountTestOrderModalRef" />
    <AdAccountAddTestOrderModal ref="AdAccountAddTestOrderModalRef" @save-success="refresh" />
  </div>
</template>

<script setup lang="ts">
import { Message, Modal, Select, type TreeNodeData } from '@arco-design/web-vue'
import AdAccountAddModal from './AdAccountAddModal.vue'
import {
  type AdAccountQuery,
  type AdAccountResp,
  checkAdAccountNurturingStatus,
  copyBrowserReq,
  deleteAdAccount,
  exportAdAccount,
  getAdAccountDailyData,
  listAdAccount,
  recoverAdAccountUsable,
  updateAdAccountUnusableReason,
} from '@/apis/biz/adAccount'
import type { TableInstanceColumns } from '@/components/GiTable/type'
import { useDownload, useTable } from '@/hooks'
import { useDept, useDict } from '@/hooks/app'
import { getAdAccountPaymentSettingMainPage, isMobile } from '@/utils'
import has from '@/utils/has'
import AdAccountCardModal from '@/views/biz/adAccount/AdAccountCardModal.vue'
import { listBusinessManagerDict, listFbChannelDict, listTagDict, listUserDict } from '@/apis'
import type { LabelValueState } from '@/types/global'
import AdAccountBrowserCheckModal from '@/views/biz/adAccount/AdAccountBrowserCheckModal.vue'
import AdAccountBindCardModal from '@/views/biz/adAccount/AdAccountBindCardModal.vue'
import AdAccountSpentCheckModal from '@/views/biz/adAccount/AdAccountSpentCheckModal.vue'
import { useAppStore } from '@/stores'
import AdAccountUpdateBM1Modal from '@/views/biz/adAccount/AdAccountUpdateBM1Modal.vue'
import AdAccountSpentModal from '@/views/biz/adAccount/AdAccountSpentModal.vue'
import AdAccountTestOrderModal from '@/views/biz/adAccount/AdAccountTestOrderModal.vue'
import AdAccountAddTestOrderModal from '@/views/biz/adAccount/AdAccountAddTestOrderModal.vue'
import { getBusinessManagerChannelList } from '@/apis/biz/businessManagerChannel'
import AdAccountBatchUpdateModal from '@/views/biz/adAccount/AdAccountBatchUpdateModal.vue'

defineOptions({ name: 'AdAccountList' })

const {
  ad_account_status,
  ad_account_keep_status,
  ad_account_sale_status,
  ad_account_clear_status,
  timezone_type,
  ad_account_appeal_status,
  business_manager_type,
  ad_account_vo_status,
  ad_account_unusable_reason,
} = useDict('ad_account_status', 'ad_account_keep_status', 'ad_account_sale_status', 'ad_account_clear_status', 'timezone_type', 'ad_account_appeal_status', 'business_manager_type', 'ad_account_vo_status', 'ad_account_unusable_reason')
const opsTimeRange = ref([])
const queryForm = reactive<AdAccountQuery>({
  tag: undefined,
  bmIds: undefined,
  platformAdIds: undefined,
  fullCardNumber: undefined,
  timezone: undefined,
  accountStatus: undefined,
  keepStatus: undefined,
  browserNo: undefined,
  saleStatus: undefined,
  appealStatus: undefined,
  clearStatus: undefined,
  firstSpend: undefined,
  lastSpend: undefined,
  startTime: undefined,
  endTime: undefined,
  createUser: undefined,
  deptId: undefined,
  sort: ['id,desc'],
  fbChannelId: undefined,
  bmAuthTime: undefined,
  banTime: undefined,
  remark: undefined,
  bmType: undefined,
  bm1Browser: undefined,
  bm1Id: undefined,
  isRemoveAdmin: undefined,
  isLowLimit: undefined,
  tags: undefined,
  bmItemType: undefined,
  voStatus: undefined,
  saleTime: undefined,
  channelName: undefined,
  updateTime: undefined,
  unusableReason: undefined,
  usable: undefined,
})

const appStore = useAppStore()
const yanghaoMode = computed(() => appStore.adAccountMode === 2)

const {
  tableData: dataList,
  loading,
  pagination,
  search,
  selectedKeys,
  handleDelete,
  select,
  selectAll,
  refresh,
} = useTable((page) => listAdAccount({ ...queryForm, ...page }), {
  immediate: true,
  paginationOption: {
    defaultPageSize: 20,
    defaultSizeOptions: [10, 20, 50, 100],
  },
})
const onOpsTimeChange = (e: string[]) => {
  if (e && e.length > 0) {
    queryForm.startTime = `${e[0]} 00:00:00`
    queryForm.endTime = `${e[1]} 23:59:59`
  } else {
    queryForm.startTime = ''
    queryForm.endTime = ''
  }
  search()
}
const columns = yanghaoMode.value
  ? ref<TableInstanceColumns[]>([
    {
      title: '序号',
      width: 66,
      align: 'center',
      render: ({ rowIndex }) => h('span', {}, rowIndex + 1 + (pagination.current - 1) * pagination.pageSize),
      fixed: !isMobile() ? 'left' : undefined,
    },
    { title: '标签', dataIndex: 'tags', slotName: 'tags', width: 200, align: 'center' },
    { title: '登号人', dataIndex: 'createUserString', align: 'center', width: 100, ellipsis: true, tooltip: true },
    { title: '养号人', dataIndex: 'tag', slotName: 'tag', width: 100 },
    { title: '原浏览器', dataIndex: 'browserNo', slotName: 'browserNo', align: 'center', width: 100 },
    { title: '浏览器', dataIndex: 'bmBrowser', slotName: 'bmBrowser', align: 'center', width: 100 },
    { title: '广告户ID', dataIndex: 'platformAdId', slotName: 'platformAdId', width: 200 },
    { title: '标签', dataIndex: 'adTag', slotName: 'adTag', width: 200 },
    { title: '时区', dataIndex: 'timezone', slotName: 'timezone', width: 150, align: 'center' },
    { title: '国家', dataIndex: 'billCountry', slotName: 'billCountry', width: 80, align: 'center' },
    { title: '货币', dataIndex: 'billCurrency', slotName: 'billCurrency', width: 80, align: 'center' },
    { title: '测试任务', dataIndex: 'testOrderCount', slotName: 'testOrderCount', align: 'center', width: 100 },
    { title: '卡片数量', dataIndex: 'cardCount', slotName: 'cardCount', align: 'center', width: 100 },
    { title: '账号状态', dataIndex: 'accountStatus', slotName: 'accountStatus', align: 'center', width: 100 },
    { title: '养号状态', dataIndex: 'keepStatus', slotName: 'keepStatus', align: 'center', width: 100 },
    { title: '花费限额', dataIndex: 'spendCap', slotName: 'spendCap', align: 'center', width: 100 },
    { title: '总消耗', dataIndex: 'totalSpent', slotName: 'totalSpent', align: 'center', width: 120, sortable: {
      sortDirections: ['ascend', 'descend'],
      sorter: true,
    } },
    { title: '当前消耗', dataIndex: 'amountSpent', slotName: 'amountSpent', align: 'center', width: 120, sortable: {
      sortDirections: ['ascend', 'descend'],
      sorter: true,
    } },
    { title: '剩余应付', dataIndex: 'balance', slotName: 'balance', align: 'center', width: 100 },
    { title: '是否可用', dataIndex: 'usable', slotName: 'usableWithReason', width: 200 },
    { title: '更新时间', dataIndex: 'updateTime', slotName: 'updateTime', width: 180 },
    { title: '创建时间', dataIndex: 'createTime', slotName: 'createTime', width: 180 },
    { title: '备注', dataIndex: 'remark', slotName: 'remark', width: 100, ellipsis: true, tooltip: true },
    {
      title: '操作',
      dataIndex: 'action',
      slotName: 'action',
      width: 200,
      align: 'center',
      fixed: !isMobile() ? 'right' : undefined,
      show: has.hasPermOr(['biz:adAccount:detail', 'biz:adAccount:update', 'biz:adAccount:delete']),
    },
  ])
  : ref<TableInstanceColumns[]>([
    {
      title: '序号',
      width: 66,
      align: 'center',
      render: ({ rowIndex }) => h('span', {}, rowIndex + 1 + (pagination.current - 1) * pagination.pageSize),
      fixed: !isMobile() ? 'left' : undefined,
    },
    { title: '标签', dataIndex: 'tags', slotName: 'tags', width: 200, align: 'center' },
    { title: '关联BM', dataIndex: 'bmId', slotName: 'bmId', width: 180 },
    { title: 'BM类型', dataIndex: 'typeName', slotName: 'typeName', width: 180 },
    { title: '登号人', dataIndex: 'createUserString', align: 'center', width: 100, ellipsis: true, tooltip: true },
    { title: '浏览器', dataIndex: 'bmBrowser', slotName: 'bmBrowser', align: 'center', width: 100 },
    { title: '授权BM1', dataIndex: 'bm1PlatformId', slotName: 'bm1PlatformId', width: 180 },
    { title: '广告户ID', dataIndex: 'platformAdId', slotName: 'platformAdId', width: 200 },
    { title: '关联渠道', dataIndex: 'channelName', slotName: 'channelName', width: 100 },
    { title: '标签', dataIndex: 'adTag', slotName: 'adTag', width: 200 },
    { title: '时区', dataIndex: 'timezone', slotName: 'timezone', width: 150, align: 'center' },
    { title: '国家', dataIndex: 'billCountry', slotName: 'billCountry', width: 80, align: 'center' },
    { title: '测试任务', dataIndex: 'testOrderCount', slotName: 'testOrderCount', align: 'center', width: 100 },
    { title: '卡片数量', dataIndex: 'cardCount', slotName: 'cardCount', align: 'center', width: 100 },
    { title: '账号状态', dataIndex: 'accountStatus', slotName: 'accountStatus', align: 'center', width: 100 },
    { title: '养号状态', dataIndex: 'keepStatus', slotName: 'keepStatus', align: 'center', width: 100 },
    { title: '出售状态', dataIndex: 'saleStatus', slotName: 'saleStatus', align: 'center', width: 100 },
    { title: '出售时间', dataIndex: 'saleTime', slotName: 'saleTime', align: 'center', width: 180 },
    { title: '清零状态', dataIndex: 'clearStatus', slotName: 'clearStatus', align: 'center', width: 100 },
    { title: '申诉状态', dataIndex: 'appealStatus', slotName: 'appealStatus', align: 'center', width: 100 },
    { title: 'bm5授权时间', dataIndex: 'bmAuthTime', slotName: 'bmAuthTime', width: 180 },
    { title: '封禁时间', dataIndex: 'banTime', slotName: 'banTime', width: 180 },
    { title: '每日限额', dataIndex: 'realAdtrustDsl', slotName: 'realAdtrustDsl', align: 'center', width: 100 },
    { title: '花费限额', dataIndex: 'spendCap', slotName: 'spendCap', align: 'center', width: 100 },
    { title: '总消耗', dataIndex: 'totalSpent', slotName: 'totalSpent', align: 'center', width: 120, sortable: {
      sortDirections: ['ascend', 'descend'],
      sorter: true,
    } },
    { title: '当前消耗', dataIndex: 'amountSpent', slotName: 'amountSpent', align: 'center', width: 120, sortable: {
      sortDirections: ['ascend', 'descend'],
      sorter: true,
    } },
    { title: '剩余应付', dataIndex: 'balance', slotName: 'balance', align: 'center', width: 100 },
    { title: '是否可用', dataIndex: 'usable', slotName: 'usableWithReason', width: 200 },
    { title: '更新时间', dataIndex: 'updateTime', slotName: 'updateTime', width: 180 },
    { title: '创建时间', dataIndex: 'createTime', slotName: 'createTime', width: 180 },
    { title: '备注', dataIndex: 'remark', slotName: 'remark', width: 100, ellipsis: true, tooltip: true },
    {
      title: '操作',
      dataIndex: 'action',
      slotName: 'action',
      width: 200,
      align: 'center',
      fixed: !isMobile() ? 'right' : undefined,
      show: has.hasPermOr(['biz:adAccount:detail', 'biz:adAccount:update', 'biz:adAccount:delete']),
    },
  ])
// 重置
const reset = () => {
  queryForm.tag = undefined
  queryForm.bmIds = undefined
  queryForm.platformAdIds = undefined
  queryForm.fullCardNumber = undefined
  queryForm.timezone = undefined
  queryForm.accountStatus = undefined
  queryForm.keepStatus = undefined
  queryForm.saleStatus = undefined
  queryForm.clearStatus = undefined
  queryForm.usable = undefined
  queryForm.unusableReason = undefined
  search()
}

const onSorterChange = (dataIndex: string, direction: string) => {
  let defaultSort = ['id,desc']
  if (direction) {
    defaultSort = [dataIndex, direction === 'descend' ? 'desc' : 'asc']
  }
  queryForm.sort = defaultSort
  search()
}

const copyBrowser = async (type: number) => {
  let text = ''
  const params = {
    ...queryForm,
    ids: selectedKeys.value,
    type,
  }
  if (type === 2) {
    dataList.value.forEach((item) => {
      if (selectedKeys.value.includes(item.id)) {
        if (item.browserNo) {
          text += `${item.browserNo},`
        }
      }
    })
  } else {
    const res = await copyBrowserReq(params)
    if (res.data.length === 0) {
      Message.info('暂无需要复制的编号')
      return
    }
    text = res.data.join(',')
  }
  try {
    await navigator.clipboard.writeText(text)
    Message.success('复制成功')
  } catch (err) {
    Message.error('复制失败')
  }
}
// 删除
const onDelete = (record: AdAccountResp) => {
  return handleDelete(() => deleteAdAccount(record.id), {
    content: `是否确定删除该条数据？`,
    showModal: true,
  })
}
const onMarkUnusable = (record: AdAccountResp) => {
  let selectedReason = null

  Modal.open({
    title: '标记不可用',
    content: () => h(Select, {
      options: ad_account_unusable_reason,
      placeholder: '请选择不可用原因',
      style: { width: '100%' },
      required: true,
      onChange: (value) => {
        selectedReason = value
      },
    }),
    onBeforeOk: async () => {
      if (!selectedReason) {
        Message.error('请选择不可用原因')
        return false
      }
      return true
    },
    onOk: async () => {
      try {
        await updateAdAccountUnusableReason({
          ids: [record.id],
          unusableReason: selectedReason,
        })
        Message.success('操作成功')
        refresh()
      } catch (error) {
        Message.error('操作失败')
        console.error(error)
      }
    },
  })
}

// 导出
const onExport = () => {
  useDownload(() => exportAdAccount(queryForm))
}

const AdAccountAddModalRef = ref<InstanceType<typeof AdAccountAddModal>>()

const AdAccountCardModalRef = ref<InstanceType<typeof AdAccountCardModal>>()
// 新增
const onCard = (platformAdId: string) => {
  AdAccountCardModalRef.value?.onOpen(platformAdId)
}

// 修改
const onUpdate = (record: AdAccountResp) => {
  AdAccountAddModalRef.value?.onUpdate(record.id)
}

const AdAccountBrowserCheckModalRef = ref<InstanceType<typeof AdAccountBrowserCheckModal>>()

const onBrowserCheck = () => {
  AdAccountBrowserCheckModalRef.value?.onOpen()
}

const AdAccountSpentCheckModalRef = ref<InstanceType<typeof AdAccountSpentCheckModal>>()

const AdAccountUpdateBM1ModalRef = ref<InstanceType<typeof AdAccountUpdateBM1Modal>>()
const onUpdateBm1 = () => {
  AdAccountUpdateBM1ModalRef.value?.onOpen()
}

const userList = ref<LabelValueState[]>([])

const getUserList = async () => {
  const { data } = await listUserDict()
  userList.value = data
}

const adsBrowserRefs = reactive({})
const browserLaunch = () => {
  for (let i = 0; i < selectedKeys.value.length; i++) {
    adsBrowserRefs[selectedKeys.value[i]]?.onOpen()
  }
}
const dailyDataList = ref<any[]>([])
const getDataList = async () => {
  const { data } = await getAdAccountDailyData()
  dailyDataList.value = data
}
getDataList()
getUserList()

const getAdsBrowserRef = (item: AdAccountResp) => {
  return (el) => {
    adsBrowserRefs[item.id] = toRefs(el)
  }
}

// 部门列表
const { deptList, getDeptList } = useDept()
// 过滤部门
const filterDeptOptions = (searchKey: string, nodeData: TreeNodeData) => {
  if (nodeData.title) {
    return nodeData.title.toLowerCase().includes(searchKey.toLowerCase())
  }
  return false
}
getDeptList()

// bm5渠道列表
const fbChannelList = ref<LabelValueState[]>([])
const getFbChannelList = async () => {
  const { data } = await listFbChannelDict()
  fbChannelList.value = data
}
getFbChannelList()

const AdAccountBindCardModalRef = ref<InstanceType<typeof AdAccountBindCardModal>>()

// 绑卡
const onBindCard = () => {
  const data: AdAccountResp[] = []
  dataList.value.forEach((item) => {
    if (selectedKeys.value.includes(item.id)) {
      data.push(item)
    }
  })
  AdAccountBindCardModalRef.value?.onOpen(data)
}

const bmIdInput = ref('')

const handlePlatformIdsChange = () => {
  const ids = bmIdInput.value
    .split(/[\s,]+/)
    .map((id) => id.trim())
    .filter((id) => id)
  queryForm.bmIds = ids.length > 0 ? ids.join(',') : undefined
  search()
}
const adAccountIdInput = ref('')
const handleAdAccountIdsChange = () => {
  const ids = adAccountIdInput.value
    .split(/[\s,]+/)
    .map((id) => id.trim())
    .filter((id) => id)
  queryForm.platformAdIds = ids.length > 0 ? ids.join(',') : undefined
  search()
}
// 在 script 部分添加

const AdAccountBatchUpdateModalRef = ref<InstanceType<typeof AdAccountBatchUpdateModal>>()
const onBatchUpdate = () => {
  AdAccountBatchUpdateModalRef.value?.onOpen(selectedKeys.value, yanghaoMode.value)
}

const bm1List = ref<LabelValueState[]>([])
const getBm1List = async () => {
  const { data } = await listBusinessManagerDict({ type: '7' })
  bm1List.value = data
}
getBm1List()

const AdAccountSpentModalRef = ref<InstanceType<typeof AdAccountSpentModal>>()
const onOpenSpent = (id: string) => {
  AdAccountSpentModalRef.value?.onOpen(id)
}

const AdAccountTestOrderModalRef = ref<InstanceType<typeof AdAccountTestOrderModal>>()
const onOpenTestOrder = (id: string) => {
  AdAccountTestOrderModalRef.value?.onOpen(id)
}

const tagList = ref<LabelValueState[]>([])
const getTagList = async () => {
  const { data } = await listTagDict()
  tagList.value = data
}
getTagList()

const tagRefresh = async () => {
  await getTagList()
  refresh()
}

const AdAccountAddTestOrderModalRef = ref<InstanceType<typeof AdAccountAddTestOrderModal>>()
const onOpenAddTestOrder = () => {
  AdAccountAddTestOrderModalRef.value?.onOpen(selectedKeys.value)
}

const checkAdAccountNurturingStatusLoading = ref(false)
const onCheckAdAccountNurturingStatus = async () => {
  checkAdAccountNurturingStatusLoading.value = true
  await checkAdAccountNurturingStatus({
    ids: selectedKeys.value,
  })
  checkAdAccountNurturingStatusLoading.value = false
  Message.success('后台检测中，请稍后查看')
}
const BM5ChannelList = ref<any[]>([])
const BM5ChannelOption = reactive<Array<{ value: string | number, label: string }>>([])
const getBM5Channel = async () => {
  const res = await getBusinessManagerChannelList()
  BM5ChannelList.value = res.data
  // 使用 map 构建 option
  const newOptions = BM5ChannelList.value.map((item) => ({
    value: item.name,
    label: item.name,
  }))

  // 更新 option
  BM5ChannelOption.length = 0
  BM5ChannelOption.push(...newOptions)
}
getBM5Channel()

const onRecoverUnable = async (platformAdId: string) => {
  await recoverAdAccountUsable(platformAdId)
  Message.success('恢复成功')
  search()
}

const route = useRoute()
watch(
  () => route.query.adAccount,
  (newVal) => {
    if (newVal) {
      queryForm.platformAdIds = newVal as string | undefined
      search()
    }
  },
  { immediate: true },
)
</script>

<style scoped lang="scss">
.statistic-container {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  text-align: center
}

a-statistic {
  flex: 1 1 auto;
  min-width: 200px;
}

.daily-title {
  color: var(--color-text-1);
  font-size: 18px;
  font-weight: 500;
  line-height: 1.5;
}
</style>
