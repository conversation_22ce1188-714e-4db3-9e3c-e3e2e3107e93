<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 1100 ? 1100 : '100%'"
    draggable
    @before-ok="save"
    @close="reset"
  >
    <GiForm ref="formRef" v-model="form" :options="options" :columns="columns" />
  </a-modal>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import { useWindowSize } from '@vueuse/core'
import { addBusinessManager, getBusinessManager, updateBusinessManager } from '@/apis/biz/businessManager'
import { type Columns, GiForm, type Options } from '@/components/GiForm'
import { useResetReactive } from '@/hooks'
import { getBusinessManagerChannelList } from '@/apis/biz/businessManagerChannel'
import { useDict } from '@/hooks/app'
import type {LabelValueState} from "@/types/global";
import {listProfitTypeDict} from "@/apis";

const emit = defineEmits<{
  (e: 'save-success'): void
}>()

const { width } = useWindowSize()

const { pa_after_sale_status, bm_invalid_reason, business_manager_status } = useDict('pa_after_sale_status', 'bm_invalid_reason', 'business_manager_status')

const dataId = ref('')
const visible = ref(false)
const isUpdate = computed(() => !!dataId.value)
const title = computed(() => (isUpdate.value ? '修改BM账号' : '新增BM账号'))
const formRef = ref<InstanceType<typeof GiForm>>()

const options: Options = {
  form: { size: 'large' },
  btns: { hide: true },
}

const profitTypeList = ref<LabelValueState[]>([])

const getProfitTypeList = async () => {
  const { data } = await listProfitTypeDict({ adPlatform: '1', cat: 'ad_account' })
  profitTypeList.value = data
}

const [form, resetForm] = useResetReactive({
  // todo 待补充
  isUse: false,
  isExternal: false,
  isEnterpriseAuth: false,
  status: 1,
  type: undefined,
  isRemoveAdmin: false,
})

const option = reactive<Array<{ value: string | number, label: string }>>([])

const useOptions = [{
  label: '是',
  value: true,
}, {
  label: '否',
  value: false,
}]

const columns: Columns = reactive<Columns>([
  {
    label: '关联渠道',
    field: 'channelId',
    type: 'select',
    options: option,
    rules: [{ required: true, message: '请输入关联渠道' }],
    props: {
      allowSearch: true,
    },
  },
  {
    label: 'BM ID',
    field: 'platformId',
    type: 'input',
    rules: [{ required: true, message: '请输入BM ID' }],
  },
  {
    label: '类型',
    field: 'type',
    type: 'select',
    options: profitTypeList,
    rules: [{ required: true, message: '请输入BM ID' }],
  },
  {
    label: '带企业认证',
    field: 'isEnterpriseAuth',
    type: 'radio-group',
    options: useOptions,
  },
  {
    label: '外部渠道号',
    field: 'isExternal',
    type: 'radio-group',
    options: useOptions,
  },
  {
    label: '移除管理员',
    field: 'isRemoveAdmin',
    type: 'radio-group',
    options: useOptions,
    rules: [{ required: true, message: '请选择' }],
  },
  {
    label: '操作号',
    field: 'opsBrowser',
    type: 'input',
    formItemProps: {
      help: '比特浏览器请填写ID，不是编号',
    },
  },
  {
    label: '备用号',
    field: 'reserveBrowser',
    type: 'input',
    formItemProps: {
      help: '比特浏览器请填写ID，不是编号',
    },
  },
  {
    label: '备用号2',
    field: 'reserveBrowserBak',
    type: 'input',
    formItemProps: {
      help: '比特浏览器请填写ID，不是编号',
    },
  },
  {
    label: '观察号',
    field: 'observeBrowser',
    type: 'input',
    formItemProps: {
      help: '比特浏览器请填写ID，不是编号',
    },
  },
  {
    label: '补号',
    field: 'isBu',
    type: 'radio-group',
    options: useOptions,
  },
  {
    label: '账号信息',
    field: 'content',
    type: 'textarea',
    rules: [{ required: true, message: '请输入账号信息' }],
    props: {
      maxLength: 1024,
    },
  },
  {
    label: '账号状态',
    field: 'status',
    type: 'select',
    options: business_manager_status,
    rules: [{ required: true, message: '请选择账号状态' }],
  },
  {
    label: '可使用坑位',
    field: 'num',
    type: 'input-number',
    hide: () => isUpdate.value,
    props: {
      min: 0,
    },
    formItemProps: {
      help: '填写默认创建对应数量的坑位，权限默认认领',
    },
  },
  {
    label: '使用情况',
    field: 'isUse',
    type: 'radio-group',
    options: useOptions,
  },
  {
    label: '使用者',
    field: 'user',
    type: 'input',
    hide: () => !form.isUse,
  },
  {
    label: '使用时间',
    field: 'useTime',
    type: 'date-picker',
    hide: () => !form.isUse,
    props: {
      showTime: true,
    },
  },
  {
    label: '售后状态',
    field: 'afterSaleStatus',
    type: 'select',
    options: pa_after_sale_status,
    rules: [{ required: true, message: '请输入售后状态' }],
  },
  {
    label: '售后原因',
    field: 'afterSaleReason',
    type: 'textarea',
    props: {
      autoSize: true,
    },
  },
  {
    label: '单价',
    field: 'unitPrice',
    type: 'cny-to-usd-input',
    rules: [{ required: true, message: '请填写价格' }],
  },
  {
    label: '备注',
    field: 'remark',
    type: 'textarea',
  },
  {
    label: '封禁原因',
    field: 'bannedReason',
    type: 'select',
    options: bm_invalid_reason,
    hide: () => !isUpdate.value || form.status !== 2, // 只在编辑模式且状态为封禁时显示
  },
])

// 重置
const reset = () => {
  formRef.value?.formRef?.resetFields()
  resetForm()
}

// 保存
const save = async () => {
  try {
    const isInvalid = await formRef.value?.formRef?.validate()
    if (isInvalid) return false
    if (isUpdate.value) {
      await updateBusinessManager(form, dataId.value)
      Message.success('修改成功')
    } else {
      await addBusinessManager(form)
      Message.success('新增成功')
    }
    emit('save-success')
    return true
  } catch (error) {
    return false
  }
}

const BM5ChannelList = ref<any[]>([])
const getBM5Channel = async () => {
  const res = await getBusinessManagerChannelList()
  BM5ChannelList.value = res.data
  // 使用 map 构建 option
  const newOptions = BM5ChannelList.value.map((item) => ({
    value: item.id,
    label: item.name,
  }))

  // 更新 option
  option.length = 0
  option.push(...newOptions)
}

// 新增
const onAdd = async () => {
  reset()
  dataId.value = ''
  await getBM5Channel()
  await getProfitTypeList()
  visible.value = true
}

// 修改
const onUpdate = async (id: string) => {
  reset()
  dataId.value = id
  const { data } = await getBusinessManager(id)
  Object.assign(form, data)
  await getBM5Channel()
  await getProfitTypeList()
  visible.value = true
}
defineExpose({ onAdd, onUpdate })
</script>

<style scoped lang="scss"></style>
