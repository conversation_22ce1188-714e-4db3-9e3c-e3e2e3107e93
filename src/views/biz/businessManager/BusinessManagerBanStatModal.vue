<template>
  <a-modal
    v-model:visible="visible"
    title="BM封禁分析"
    :mask-closable="false"
    :footer="false"
    :width="width >= 1100 ? 1100 : '100%'"
    @close="reset"
  >
    <div class="statistics-container">
      <!-- 筛选条件 -->
      <div class="filter-section mb-4">
        <a-form :model="queryForm" layout="inline">
          <a-form-item label="渠道">
            <a-select
              v-model="queryForm.channelId"
              placeholder="请选择渠道"
              allow-clear
              allow-search
              style="width: 150px"
            >
              <a-option
                v-for="item in BM5ChannelList"
                :key="item.id"
                :value="item.id"
                :label="item.name"
              />
            </a-select>
          </a-form-item>
          <!-- 新增BM类型筛选 -->
          <a-form-item label="BM类型">
            <AdAccountTypeSelect v-model="queryForm.type" @change="search"></AdAccountTypeSelect>
          </a-form-item>
          <a-form-item label="按渠道分组">
            <a-switch
              v-model="queryForm.groupByChannel"
              @change="handleGroupChange"
            />
          </a-form-item>
          <a-form-item label="创建时间">
            <DateRangePicker
              v-model="queryForm.statisticsDate"
              :show-time="false"
              @change="onStatTimeChange"
            />
          </a-form-item>

          <a-form-item label="封禁时间">
            <DateRangePicker
              v-model="queryForm.bannedDate"
              :show-time="false"
              @change="onBannedTimeChange"
            />
          </a-form-item>

          <a-form-item>
            <a-button type="primary" @click="search">
              <template #icon><icon-search /></template>
              查询
            </a-button>
            <a-button style="margin-left: 8px" @click="reset">
              <template #icon><icon-refresh /></template>
              重置
            </a-button>
          </a-form-item>
        </a-form>
      </div>

      <!-- 表格展示 -->
      <GiTable
        row-key="id"
        :data="dataList"
        :columns="columns"
        :loading="loading"
        :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
        :pagination="pagination"
        @refresh="search"
      >
        <template #bannedReason="{ record }">
          <GiCellTag :value="record.bannedReason" :dict="bm_invalid_reason" />
        </template>
      </GiTable>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useWindowSize } from '@vueuse/core'
import { listBusinessManagerBanStat } from '@/apis/biz/businessManagerStatistics'
import { useTable } from '@/hooks'
import { getBusinessManagerChannelList } from '@/apis/biz/businessManagerChannel'
import { useDict } from '@/hooks/app'

const { width } = useWindowSize()
const visible = ref(false)

const { bm_invalid_reason } = useDict('bm_invalid_reason')

const queryForm = reactive({
  channelId: undefined,
  type: undefined, // 新增BM类型字段
  statisticsDate: undefined,
  bannedDate: undefined,
  groupByChannel: true,
})

// 添加分组切换处理方法
const handleGroupChange = () => {
  // 更新列配置
  if (queryForm.groupByChannel) {
    columns.value = [
      { title: '渠道名称', dataIndex: 'channelName', width: 120 },
      { title: '封禁原因', dataIndex: 'bannedReason', slotName: 'bannedReason', width: 180, tooltip: true, ellipsis: true },
      { title: '封禁数量', dataIndex: 'bannedCount', width: 100 },
    ]
  } else {
    columns.value = [
      { title: '封禁原因', dataIndex: 'bannedReason', slotName: 'bannedReason', width: 180, tooltip: true, ellipsis: true },
      { title: '封禁数量', dataIndex: 'bannedCount', width: 100 },
    ]
  }
  // 重新查询数据
  search()
}

const reset = () => {
  queryForm.channelId = undefined
  queryForm.type = undefined // 新增重置BM类型
  queryForm.statisticsDate = undefined
  queryForm.bannedDate = undefined
  queryForm.groupByChannel = false
  handleGroupChange()
}

const BM5ChannelList = ref<any[]>([])
const getBM5Channel = async () => {
  const res = await getBusinessManagerChannelList()
  BM5ChannelList.value = res.data
}

const {
  tableData: dataList,
  loading,
  pagination,
  search,
} = useTable((page) => listBusinessManagerBanStat({ ...queryForm, ...page }), { immediate: false })

const onStatTimeChange = (value) => {
  if (value && value.length > 0) {
    queryForm.statisticsDate = value
  } else {
    queryForm.statisticsDate = undefined
  }
}

const onBannedTimeChange = (value) => {
  if (value && value.length > 0) {
    queryForm.bannedDate = value
  } else {
    queryForm.bannedDate = undefined
  }
}

const columns = ref([
  { title: '渠道名称', dataIndex: 'channelName', width: 120 },
  { title: '封禁原因', dataIndex: 'bannedReason', slotName: 'bannedReason', width: 180, tooltip: true, ellipsis: true },
  { title: '封禁数量', dataIndex: 'bannedCount', width: 100 },
])

const onOpen = async () => {
  visible.value = true
  await getBM5Channel()
  search()
}

defineExpose({ onOpen })
</script>

<style scoped lang="scss">
.statistics-container {
  min-height: 500px;
}
</style>
