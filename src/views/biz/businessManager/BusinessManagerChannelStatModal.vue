<template>
  <a-modal
    v-model:visible="visible"
    title="BM渠道分析"
    :mask-closable="false"
    :footer="false"
    :width="width >= 1100 ? 1100 : '100%'"
    @close="reset"
  >
    <div class="statistics-container">
      <!-- 筛选条件 -->
      <div class="filter-section mb-4">
        <a-form :model="queryForm" layout="inline">
          <a-form-item label="渠道">
            <a-select
              v-model="queryForm.channelId"
              placeholder="请选择渠道"
              allow-clear
              allow-search
              style="width: 150px"
            >
              <a-option
                v-for="item in BM5ChannelList"
                :key="item.id"
                :value="item.id"
                :label="item.name"
              />
            </a-select>
          </a-form-item>
          <a-form-item label="BM类型">
            <AdAccountTypeSelect v-model="queryForm.bmType" @change="search"></AdAccountTypeSelect>
          </a-form-item>
          <a-form-item label="BM创建时间">
            <DateRangePicker
              v-model="queryForm.statisticsDate"
              :show-time="false"
              :default-value="defaultDateRange"
              @change="onStatTimeChange"
            />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="search">
              <template #icon><icon-search /></template>
              查询
            </a-button>
            <a-button style="margin-left: 8px" @click="reset">
              <template #icon><icon-refresh /></template>
              重置
            </a-button>
          </a-form-item>
        </a-form>
      </div>

      <!-- 表格展示 -->
      <GiTable
        row-key="id"
        :data="dataList"
        :columns="columns"
        :loading="loading"
        :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
        :pagination="pagination"
        @refresh="search"
      />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useWindowSize } from '@vueuse/core'
import dayjs from 'dayjs'
import { listBusinessManagerChannelStat } from '@/apis/biz/businessManagerStatistics'
import { useTable } from '@/hooks'
import { getBusinessManagerChannelList } from '@/apis/biz/businessManagerChannel'
import { useDict } from '@/hooks/app'
import AdAccountTypeSelect from '@/components/AdAccountTypeSelect/index.vue'

const { width } = useWindowSize()
const visible = ref(false)

// 设置默认日期为昨天
const defaultDateRange = [
  dayjs().subtract(1, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
  dayjs().subtract(1, 'day').endOf('day').format('YYYY-MM-DD HH:mm:ss'),
]

const { business_manager_type } = useDict('business_manager_type')

const queryForm = reactive({
  channelId: undefined,
  bmType: 1,
  statisticsDate: defaultDateRange, // 默认值设为昨天
})

const BM5ChannelList = ref<any[]>([])
const getBM5Channel = async () => {
  const res = await getBusinessManagerChannelList()
  BM5ChannelList.value = res.data
}

const {
  tableData: dataList,
  loading,
  pagination,
  search,
} = useTable((page) => listBusinessManagerChannelStat({ ...queryForm, ...page }), { immediate: false })

const onStatTimeChange = (value) => {
  if (value && value.length > 0) {
    queryForm.statisticsDate = value
  } else {
    queryForm.statisticsDate = undefined
  }
}

const reset = () => {
  queryForm.channelId = undefined
  queryForm.statisticsDate = defaultDateRange
  queryForm.bmType = 1
}

const columns = ref([
  { title: '渠道名称', dataIndex: 'channelName', width: 120 },
  { title: '总数', dataIndex: 'totalCount', width: 100 },
  { title: '剩余可用数', dataIndex: 'availableCount', width: 120 },
  { title: '封禁BM数', dataIndex: 'bannedCount', width: 120 },
  { title: '掉额BM数', dataIndex: 'lowerLimitCount', width: 120 },
  { title: '已使用封禁BM数', dataIndex: 'usedBannedCount', width: 150 },
  { title: '掉额广告户数', dataIndex: 'lowerLimitAdCount', width: 150 },
  { title: '总户数', dataIndex: 'adAccountTotalCount', width: 150 },
  { title: '作废户数', dataIndex: 'adAccountInvalidCount', width: 150 },
  { title: '作废占比', dataIndex: 'adAccountInvalidRatio', width: 150 },
  { title: '下户数', dataIndex: 'adAccountOrderCount', width: 150 },
  { title: '不消耗数', dataIndex: 'adAccountOrderNoSpendCount', width: 150 },
  { title: '不消耗占比', dataIndex: 'adAccountOrderNoSpendRatio', width: 150 },

])

const onOpen = async () => {
  visible.value = true
  await getBM5Channel()
  search()
}

defineExpose({ onOpen })
</script>

<style scoped lang="scss">
.statistics-container {
  min-height: 500px;
}
</style>
