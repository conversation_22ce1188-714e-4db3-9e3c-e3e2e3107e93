<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 600 ? 600 : '100%'"
    draggable
    @before-ok="save"
    @close="reset"
  >
    <GiForm ref="formRef" v-model="form" :options="options" :columns="columns" />
  </a-modal>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import { useWindowSize } from '@vueuse/core'
import { type Columns, GiForm, type Options } from '@/components/GiForm'
import { useResetReactive } from '@/hooks'
import { getBusinessManagerChannelList } from '@/apis/biz/businessManagerChannel'
import { useDict } from '@/hooks/app'
import { addItem } from '@/apis/biz/businessManagerItem'
import type { BusinessManagerResp } from '@/apis/biz/businessManager'
import type {LabelValueState} from "@/types/global";
import {listProfitTypeDict} from "@/apis";

const emit = defineEmits<{
  (e: 'save-success'): void
}>()

const { width } = useWindowSize()

const dataId = ref('')
const visible = ref(false)
const title = ref('新增坑位')
const formRef = ref<InstanceType<typeof GiForm>>()

const options: Options = {
  form: { size: 'large' },
  btns: { hide: true },
}

const [form, resetForm] = useResetReactive({
  // todo 待补充
  bmId: computed(() => dataId.value),

})

const profitTypeList = ref<LabelValueState[]>([])

const getProfitTypeList = async () => {
  const { data } = await listProfitTypeDict({ adPlatform: '1', cat: 'ad_account' })
  profitTypeList.value = data
}

const option = reactive<Array<{ value: string | number, label: string }>>([])

const columns: Columns = reactive<Columns>([

  {
    label: '单价',
    field: 'unitPrice',
    type: 'input-number',
    rules: [{ required: true, message: '请输入单价' }],
    formItemProps: {
      help: '请注意是单价！！！单价！！！单价！！！，不要填成总价',
    },
  },
  {
    label: '类型',
    field: 'type',
    type: 'select',
    options: profitTypeList,
    rules: [{ required: true, message: '请选择' }],
  },
  {
    label: '关联渠道',
    field: 'channelId',
    type: 'select',
    options: option,
    rules: [{ required: true, message: '请输入关联渠道' }],
    props: {
      allowSearch: true,
    },
  },
  {
    label: '权限',
    field: 'ownMethod',
    type: 'select',
    options: [
      { label: '认领', value: 1 },
      { label: '授权', value: 2 },
    ],
    rules: [{ required: true, message: '请选择权限' }],
    props: {
      allowSearch: true,
    },
  },
  {
    label: '可使用坑位',
    field: 'num',
    type: 'input-number',
    props: {
      min: 0,
    },
    rules: [{ required: true, message: '请输入可使用坑位' }],
  },
  {
    label: '备注',
    field: 'remark',
    type: 'input',
  },
])

// 重置
const reset = () => {
  formRef.value?.formRef?.resetFields()
  resetForm()
}

// 保存
const save = async () => {
  try {
    const isInvalid = await formRef.value?.formRef?.validate()
    if (isInvalid) return false
    await addItem(form)
    Message.success('新增成功')

    emit('save-success')
    return true
  } catch (error) {
    return false
  }
}

// 新增
const onAdd = async (record: BusinessManagerResp) => {
  reset()
  dataId.value = record.id
  form.type = record.type
  form.channelId = record.channelId
  await getProfitTypeList()
  visible.value = true
}

const BM5ChannelList = ref<any[]>([])
const getBM5Channel = async () => {
  const res = await getBusinessManagerChannelList()
  BM5ChannelList.value = res.data
  // 使用 map 构建 option
  const newOptions = BM5ChannelList.value.map((item) => ({
    value: item.id,
    label: item.name,
  }))

  // 更新 option
  option.length = 0
  option.push(...newOptions)
}
onMounted(() => {
  getBM5Channel()
})
defineExpose({ onAdd })
</script>

<style scoped lang="scss"></style>
