<template>
  <a-modal
    v-model:visible="visible"
    title="交易流水统计"
    :mask-closable="false"
    :footer="false"
    :width="width >= 1300 ? 1300 : '100%'"
    @close="reset"
  >
    <div class="statistics-container">
      <!-- 筛选条件 -->
      <div class="filter-section mb-4">
        <a-form :model="queryForm" layout="inline">
          <a-form-item label="卡台">
            <a-select
              v-model="queryForm.platform"
              :options="card_platform"
              placeholder="请选择卡台"
              allow-clear
              style="width: 150px"
            />
          </a-form-item>
          <a-form-item label="广告平台">
            <a-select
              v-model="queryForm.adPlatform"
              :options="ad_platform"
              placeholder="请选择广告平台"
              allow-clear
              style="width: 150px"
            />
          </a-form-item>
          <a-form-item label="统计时间">
            <DateRangePicker :show-time="false" @change="onStatTimeChange" />
          </a-form-item>

          <a-form-item>
            <a-button type="primary" @click="search">
              <template #icon><icon-search /></template>
              查询
            </a-button>
            <a-button style="margin-left: 8px" @click="reset">
              <template #icon><icon-refresh /></template>
              重置
            </a-button>
          </a-form-item>
        </a-form>
      </div>

      <!-- 汇总数据 -->
      <a-card class="mb-4" title="汇总数据" :bordered="false">
        <a-descriptions :column="3">
          <a-descriptions-item label="总开卡数量">{{ summaryData.totalCardCount || 0 }}</a-descriptions-item>
          <a-descriptions-item label="总卡片余额">{{ summaryData.totalBalance || 0 }}</a-descriptions-item>
          <a-descriptions-item label="总消耗">{{ summaryData.totalSpend || 0 }}</a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- Tab切换 -->
      <a-tabs v-model:activeKey="activeTab" :justify="true" @change="tabSearch">
        <a-tab-pane key="date" title="每日统计">
          <CardTransactionStatByDate ref="CardTransactionStatByDateRef" />
        </a-tab-pane>
        <a-tab-pane key="timezone" title="时区统计">
          <CardTransactionStatByTimezone ref="CardTransactionStatByTimezoneRef" />
        </a-tab-pane>
        <a-tab-pane key="user" title="用户维度统计">
          <CardTransactionStatByCardholder ref="CardTransactionStatByCardholderRef" />
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useWindowSize } from '@vueuse/core'
import { type CardTransactionStatisticsQuery, getCardTransactionStatisticsSummary } from '@/apis/biz/cardTransaction'
import { useDict } from '@/hooks/app'
import CardTransactionStatByCardholder
  from '@/views/biz/cardTransaction/components/CardTransactionStatByCardholder.vue'
import CardTransactionStatByDate from '@/views/biz/cardTransaction/components/CardTransactionStatByDate.vue'
import CardTransactionStatByTimezone from '@/views/biz/cardTransaction/components/CardTransactionStatByTimezone.vue'

defineOptions({ name: 'CardTransactionStatisticsModal' })

const visible = ref(false)
const activeTab = ref('date')
const { width } = useWindowSize()

const { card_platform, ad_platform } = useDict('card_platform', 'ad_platform')

const CardTransactionStatByCardholderRef = ref<InstanceType<typeof CardTransactionStatByCardholder>>()
const CardTransactionStatByDateRef = ref<InstanceType<typeof CardTransactionStatByDate>>()
const CardTransactionStatByTimezoneRef = ref<InstanceType<typeof CardTransactionStatByTimezone>>()

// 查询表单
const queryForm = reactive<CardTransactionStatisticsQuery>({
  platform: undefined,
  adPlatform: undefined,
  startTime: undefined,
  endTime: undefined,
})

// 汇总数据
const summaryData = reactive({
  totalCardCount: 0,
  totalSpend: 0,
  totalBalance: 0,
})

const getSummary = async () => {
  const { data } = await getCardTransactionStatisticsSummary(queryForm)
  summaryData.totalCardCount = data.totalCardCount
  summaryData.totalSpend = data.totalSpend
  summaryData.totalBalance = data.totalBalance
}

const onStatTimeChange = (value) => {
  if (value && value.length > 0) {
    queryForm.startTime = value[0]
    queryForm.endTime = value[1]
  } else {
    queryForm.startTime = undefined
    queryForm.endTime = undefined
  }
}

const tabSearch = () => {
  if (activeTab.value === 'user') {
    CardTransactionStatByCardholderRef.value?.onOpen(queryForm)
  } else if (activeTab.value === 'date') {
    CardTransactionStatByDateRef.value?.onOpen(queryForm)
  } else if (activeTab.value === 'timezone') {
    CardTransactionStatByTimezoneRef.value?.onOpen(queryForm)
  }
}

const search = () => {
  getSummary()
  tabSearch()
}

// 重置方法
const reset = () => {
  queryForm.platform = undefined
  queryForm.startTime = undefined
  queryForm.endTime = undefined
}

// 修改
const onOpen = async () => {
  visible.value = true
  search()
}

defineExpose({ onOpen })
</script>

<style scoped lang="scss">
.statistics-container {
  min-height: 500px;

  :deep(.gi-table__toolbar-right) {
    display: none;
  }
}

.mb-4 {
  margin-bottom: 16px;
}

.mb-2 {
  margin-bottom: 8px;
}
</style>
