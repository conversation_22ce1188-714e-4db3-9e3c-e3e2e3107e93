<template>
  <div class="table-page">
    <GiTable
      title="客户退款订单管理"
      row-key="id"
      :data="dataList"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
      :pagination="pagination"
      :disabled-tools="['size']"
      :disabled-column-keys="['name']"
      @refresh="refresh"
    >
      <template #toolbar-left>
        <a-input-search v-model="queryForm.orderNo" placeholder="请输入订单编号" allow-clear @search="search" />
        <a-select
          v-model="queryForm.status"
          :options="customer_withdraw_order_status"
          placeholder="请选择订单状态"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <a-select
          v-model="queryForm.customerId"
          :options="customerList"
          placeholder="请选择关联客户"
          allow-clear
          allow-search
          style="width: 150px"
          @change="search"
        />
        <a-select
          v-model="queryForm.createUser"
          :options="userList"
          placeholder="请选择创建人"
          allow-clear
          allow-search
          style="width: 150px"
          @change="search"
        />
        <DateRangePicker
          v-model="queryForm.expectedRefundTime"
          :show-time="false"
          :placeholder="['预计退款开始时间', '预计退款结束时间']"
          @change="search"
        />
        <DateRangePicker
          v-model="queryForm.createTime"
          :show-time="false"
          :placeholder="['创建开始时间', '创建结束时间']"
          @change="search"
        />
        <a-button @click="reset">
          <template #icon><icon-refresh /></template>
          <template #default>重置</template>
        </a-button>
      </template>
      <template #toolbar-right>
        <a-button v-permission="['biz:customerWithdrawOrder:add']" type="primary" @click="onAdd">
          <template #icon><icon-plus /></template>
          <template #default>新增</template>
        </a-button>
        <a-button v-permission="['biz:customerWithdrawOrder:export']" @click="onExport">
          <template #icon><icon-download /></template>
          <template #default>导出</template>
        </a-button>
      </template>
      <template #action="{ record }">
        <a-space>
          <a-link
            v-if="record.status === 1"
            v-permission="['biz:customerWithdrawOrder:audit']"
            title="审核"
            @click="onAudit(record)"
          >
            审核
          </a-link>
          <a-link
            v-if="record.status === 2"
            v-permission="['biz:customerWithdrawOrder:refund']"
            title="退款"
            @click="onRefund(record)"
          >
            退款
          </a-link>
          <a-link
            v-if="record.status === 1"
            v-permission="['biz:customerWithdrawOrder:delete']"
            status="danger"
            :disabled="record.disabled"
            :title="record.disabled ? '不可删除' : '删除'"
            @click="onDelete(record)"
          >
            删除
          </a-link>
        </a-space>
      </template>

      <template #status="{ record }">
        <GiCellTag :value="record.status" :dict="customer_withdraw_order_status" />
      </template>
    </GiTable>

    <CustomerWithdrawOrderAddModal ref="CustomerWithdrawOrderAddModalRef" @save-success="search" />
    <CustomerWithdrawOrderAuditModal ref="CustomerWithdrawOrderAuditModalRef" @save-success="refresh" />
    <CustomerWithdrawOrderRefundModal ref="CustomerWithdrawOrderRefundModalRef" @save-success="refresh" />
    <CustomerWithdrawOrderDetailDrawer ref="CustomerWithdrawOrderDetailDrawerRef" />
  </div>
</template>

<script setup lang="ts">
import CustomerWithdrawOrderAddModal from './CustomerWithdrawOrderAddModal.vue'
import CustomerWithdrawOrderAuditModal from './CustomerWithdrawOrderAuditModal.vue'
import CustomerWithdrawOrderRefundModal from './CustomerWithdrawOrderRefundModal.vue'
import CustomerWithdrawOrderDetailDrawer from './CustomerWithdrawOrderDetailDrawer.vue'
import { type CustomerWithdrawOrderQuery, type CustomerWithdrawOrderResp, deleteCustomerWithdrawOrder, exportCustomerWithdrawOrder, listCustomerWithdrawOrder } from '@/apis/biz/customerWithdrawOrder'
import type { TableInstanceColumns } from '@/components/GiTable/type'
import { useDownload, useTable } from '@/hooks'
import { useDict } from '@/hooks/app'
import { useCustomer } from '@/hooks/app/useCustomer'
import { listUserDict } from '@/apis'
import type { LabelValueState } from '@/types/global'
import { isMobile } from '@/utils'
import has from '@/utils/has'
import DateRangePicker from '@/components/DateRangePicker/index.vue'

defineOptions({ name: 'CustomerWithdrawOrder' })

const { customer_withdraw_order_status } = useDict('customer_withdraw_order_status')
const { customerList, getCustomerList } = useCustomer()

const queryForm = reactive<CustomerWithdrawOrderQuery>({
  orderNo: undefined,
  status: undefined,
  customerId: undefined,
  expectedRefundTime: undefined,
  createTime: undefined,
  createUser: undefined,
  sort: ['id,desc'],
})

const userList = ref<LabelValueState[]>([])
const getUserList = async () => {
  const { data } = await listUserDict()
  userList.value = data
}

getUserList()
getCustomerList()

const {
  tableData: dataList,
  loading,
  pagination,
  search,
  handleDelete,
  refresh,
} = useTable((page) => listCustomerWithdrawOrder({ ...queryForm, ...page }), { immediate: true })
const columns = ref<TableInstanceColumns[]>([
  {
    title: '序号',
    width: 66,
    align: 'center',
    render: ({ rowIndex }) => h('span', {}, rowIndex + 1 + (pagination.current - 1) * pagination.pageSize),
    fixed: !isMobile() ? 'left' : undefined,
  },
  { title: '订单编号', dataIndex: 'orderNo', slotName: 'orderNo', width: 150, ellipsis: true, tooltip: true },
  { title: '订单状态', dataIndex: 'status', slotName: 'status', width: 100 },
  { title: '关联客户', dataIndex: 'customerName', slotName: 'customerName', width: 150 },
  { title: '预计退款时间', dataIndex: 'expectedRefundTime', slotName: 'expectedRefundTime', width: 175 },
  { title: '备注', dataIndex: 'remark', slotName: 'remark', width: 150, ellipsis: true, tooltip: true },
  { title: '创建时间', dataIndex: 'createTime', slotName: 'createTime', width: 175 },
  { title: '当前余额', dataIndex: 'customerBalance', slotName: 'customerBalance', width: 100, align: 'center' },
  { title: '创建人', dataIndex: 'createUserString', slotName: 'createUser', width: 150 },
  { title: '审核人', dataIndex: 'auditorName', slotName: 'auditorName', width: 150 },
  { title: '审核时间', dataIndex: 'auditTime', slotName: 'auditTime', width: 150 },
  { title: '审核备注', dataIndex: 'auditRemark', slotName: 'auditRemark', width: 150, ellipsis: true, tooltip: true },
  { title: '实际退款时间', dataIndex: 'actualRefundTime', slotName: 'actualRefundTime', width: 150 },
  { title: '实际退款金额', dataIndex: 'actualRefundAmount', slotName: 'actualRefundAmount', width: 150 },

  {
    title: '操作',
    dataIndex: 'action',
    slotName: 'action',
    width: 160,
    align: 'center',
    fixed: !isMobile() ? 'right' : undefined,
    show: has.hasPermOr(['biz:customerWithdrawOrder:audit', 'biz:customerWithdrawOrder:refund', 'biz:customerWithdrawOrder:delete']),
  },
])

// 重置
const reset = () => {
  queryForm.orderNo = undefined
  queryForm.status = undefined
  queryForm.customerId = undefined
  queryForm.expectedRefundTime = undefined
  queryForm.createTime = undefined
  queryForm.createUser = undefined
  search()
}

// 删除
const onDelete = (record: CustomerWithdrawOrderResp) => {
  return handleDelete(() => deleteCustomerWithdrawOrder(record.id), {
    content: `是否确定删除该条数据？`,
    showModal: true,
  })
}

// 导出
const onExport = () => {
  useDownload(() => exportCustomerWithdrawOrder(queryForm))
}

const CustomerWithdrawOrderAddModalRef = ref<InstanceType<typeof CustomerWithdrawOrderAddModal>>()
// 新增
const onAdd = () => {
  CustomerWithdrawOrderAddModalRef.value?.onAdd()
}

const CustomerWithdrawOrderAuditModalRef = ref<InstanceType<typeof CustomerWithdrawOrderAuditModal>>()
// 审核
const onAudit = (record: CustomerWithdrawOrderResp) => {
  CustomerWithdrawOrderAuditModalRef.value?.onAudit(record.id)
}

const CustomerWithdrawOrderRefundModalRef = ref<InstanceType<typeof CustomerWithdrawOrderRefundModal>>()
// 退款
const onRefund = (record: CustomerWithdrawOrderResp) => {
  CustomerWithdrawOrderRefundModalRef.value?.onRefund(record.id, record.customerBalance)
}

const CustomerWithdrawOrderDetailDrawerRef = ref<InstanceType<typeof CustomerWithdrawOrderDetailDrawer>>()
// 详情
const onDetail = (record: CustomerWithdrawOrderResp) => {
  CustomerWithdrawOrderDetailDrawerRef.value?.onOpen(record.id)
}
</script>

<style scoped lang="scss"></style>
