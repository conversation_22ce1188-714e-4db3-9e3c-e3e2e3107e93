<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 600 ? 600 : '100%'"
    draggable
    @before-ok="save"
    @close="reset"
  >
    <GiForm ref="formRef" v-model="form" :options="options" :columns="columns" />
  </a-modal>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import { useWindowSize } from '@vueuse/core'
import dayjs from 'dayjs'
import { getCustomerWithdrawOrder, preRefundCustomerWithdrawOrder, refundCustomerWithdrawOrder } from '@/apis/biz/customerWithdrawOrder'
import { type Columns, GiForm, type Options } from '@/components/GiForm'
import { useResetReactive } from '@/hooks'

const emit = defineEmits<{
  (e: 'save-success'): void
}>()

const { width } = useWindowSize()

const visible = ref(false)
const title = ref('退款')
const formRef = ref<InstanceType<typeof GiForm>>()

const options: Options = {
  form: { size: 'large' },
  btns: { hide: true },
}

const [form, resetForm] = useResetReactive({
  id: null,
  customerId: null,
  actualRefundTime: null,
  actualRefundAmount: null,
})

const columns = computed<Columns>(() => {
  return [
    {
      label: '退款时间',
      field: 'actualRefundTime',
      type: 'datePicker',
      props: {
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        placeholder: '请选择退款时间',
      },
      rules: [{ required: true, message: '请选择退款时间' }],
    },
    {
      label: '退款金额',
      field: 'actualRefundAmount',
      type: 'inputNumber',
      props: {
        placeholder: '请输入退款金额',
        precision: 2,
        step: 0.01,
        min: 0,
      },
      rules: [{ required: true, message: '请输入退款金额' }],
    },
  ]
})

// 重置
const reset = () => {
  formRef.value?.formRef?.resetFields()
  resetForm()
}

// 保存
const save = async () => {
  try {
    const isInvalid = await formRef.value?.formRef?.validate()
    if (isInvalid) return false
    await refundCustomerWithdrawOrder(form)
    Message.success('退款成功')
    emit('save-success')
    return true
  } catch (error) {
    return false
  }
}

// 退款
const onRefund = async (id: number, blance: number) => {
  reset()
  form.id = id
  form.actualRefundTime = dayjs().format('YYYY-MM-DD HH:mm:ss')

  // 获取订单详情
  const { data: orderDetail } = await getCustomerWithdrawOrder(id)
  form.customerId = orderDetail.customerId

  // 获取预退款金额
  // const { data: preRefundAmount } = await preRefundCustomerWithdrawOrder(orderDetail.customerId)
  form.actualRefundAmount = blance

  visible.value = true
}

defineExpose({ onRefund })
</script>
