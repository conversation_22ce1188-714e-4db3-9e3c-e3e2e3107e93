<template>
  <div class="table-page">
    <GiTable
      title="利润类型管理"
      row-key="id"
      :data="dataList"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
      :pagination="pagination"
      :disabled-tools="['size']"
      :disabled-column-keys="['name']"
      @refresh="search"
    >
      <template #toolbar-left>
        <a-select
          v-model="queryForm.adPlatform"
          :options="ad_platform"
          placeholder="请选择媒体平台"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <a-input-search v-model="queryForm.name" placeholder="请输入名称" allow-clear @search="search" />
        <a-button @click="reset">
          <template #icon><icon-refresh /></template>
          <template #default>重置</template>
        </a-button>
      </template>
      <template #toolbar-right>
        <a-button v-permission="['biz:profitType:add']" type="primary" @click="onAdd">
          <template #icon><icon-plus /></template>
          <template #default>新增</template>
        </a-button>
        <a-button v-permission="['biz:profitType:export']" @click="onExport">
          <template #icon><icon-download /></template>
          <template #default>导出</template>
        </a-button>
      </template>
      <template #adPlatform="{ record }">
        <GiCellTag :value="record.adPlatform" :dict="ad_platform" />
      </template>
      <template #project="{ record }">
        <GiCellTag :value="record.project" :dict="ad_project" />
      </template>
      <template #action="{ record }">
        <a-space>
          <a-link v-permission="['biz:profitType:update']" title="修改" @click="onUpdate(record)">修改</a-link>
          <a-link
            v-permission="['biz:profitType:delete']"
            status="danger"
            :disabled="record.disabled"
            :title="record.disabled ? '不可删除' : '删除'"
            @click="onDelete(record)"
          >
            删除
          </a-link>
        </a-space>
      </template>
    </GiTable>

    <ProfitTypeAddModal ref="ProfitTypeAddModalRef" @save-success="search" />
  </div>
</template>

<script setup lang="ts">
import ProfitTypeAddModal from './ProfitTypeAddModal.vue'
import { type ProfitTypeQuery, type ProfitTypeResp, deleteProfitType, exportProfitType, listProfitType } from '@/apis/biz/profitType'
import type { TableInstanceColumns } from '@/components/GiTable/type'
import { useDownload, useTable } from '@/hooks'
import { useDict } from '@/hooks/app'
import { isMobile } from '@/utils'
import has from '@/utils/has'

defineOptions({ name: 'ProfitType' })

const { ad_project, ad_platform } = useDict('ad_project', 'ad_platform')

const queryForm = reactive<ProfitTypeQuery>({
  adPlatform: undefined,
  project: undefined,
  name: undefined,
  sort: ['id,desc'],
})

const {
  tableData: dataList,
  loading,
  pagination,
  search,
  handleDelete,
} = useTable((page) => listProfitType({ ...queryForm, ...page }), { immediate: true })
const columns = ref<TableInstanceColumns[]>([
  {
    title: '序号',
    width: 66,
    align: 'center',
    render: ({ rowIndex }) => h('span', {}, rowIndex + 1 + (pagination.current - 1) * pagination.pageSize),
    fixed: !isMobile() ? 'left' : undefined,
  },
  { title: '媒体平台', dataIndex: 'adPlatform', slotName: 'adPlatform', align: 'center' },
  { title: '名称', dataIndex: 'name', slotName: 'name' },
  { title: '创建人', dataIndex: 'createUserString', slotName: 'createUser' },
  { title: '创建时间', dataIndex: 'createTime', slotName: 'createTime' },
  // {
  //   title: '操作',
  //   dataIndex: 'action',
  //   slotName: 'action',
  //   width: 160,
  //   align: 'center',
  //   fixed: !isMobile() ? 'right' : undefined,
  //   show: has.hasPermOr(['biz:profitType:detail', 'biz:profitType:update', 'biz:profitType:delete']),
  // },
])

// 重置
const reset = () => {
  queryForm.adPlatform = undefined
  queryForm.project = undefined
  queryForm.name = undefined
  search()
}

// 删除
const onDelete = (record: ProfitTypeResp) => {
  return handleDelete(() => deleteProfitType(record.id), {
    content: `是否确定删除该条数据？`,
    showModal: true,
  })
}

// 导出
const onExport = () => {
  useDownload(() => exportProfitType(queryForm))
}

const ProfitTypeAddModalRef = ref<InstanceType<typeof ProfitTypeAddModal>>()
// 新增
const onAdd = () => {
  ProfitTypeAddModalRef.value?.onAdd()
}

// 修改
const onUpdate = (record: ProfitTypeResp) => {
  ProfitTypeAddModalRef.value?.onUpdate(record.id)
}
</script>

<style scoped lang="scss"></style>
