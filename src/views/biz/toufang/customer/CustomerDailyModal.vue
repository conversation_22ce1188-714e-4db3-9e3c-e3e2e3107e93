<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 1100 ? 1100 : '100%'"
    draggable
    :footer="false"
  >
    <div class="table-page">
      <GiTable
        :key="tableKey"
        :row-key="(record, index) => index"
        :data="dataList"
        :columns="columns"
        :loading="loading"
        :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
        :pagination="pagination"
        :disabled-tools="['size']"
        :disabled-column-keys="['name']"
        @refresh="search"
      >
      </GiTable>
    </div>
  </a-modal>
</template>

<script setup lang="tsx">
import { nextTick } from 'vue'
import { useWindowSize } from '@vueuse/core'
import { toufangCustomerDailyPage } from '@/apis/biz/customer'
import { useTable } from '@/hooks'
import type { TableInstanceColumns } from '@/components/GiTable/type'

const { width } = useWindowSize()

const visible = ref(false)
const title = computed(() => ('日报'))

const customerId = ref('')
const tableKey = ref(0)
const columns = ref<TableInstanceColumns[]>([])

// 生成动态列配置
const generateColumns = (data: any[]) => {
  if (!data || data.length === 0) return []

  const firstRow = data[0]

  // 定义期望的列顺序（根据你的数据结构）
  const expectedOrder = [
    '日期', '代理线', '产品', '打款', '日消耗',
    '回流', '服务费', '实际花费', 'eqwe', 'pop123'
  ]

  // 获取实际存在的键，保持原始顺序
  const actualKeys = Object.keys(firstRow)

  // 按照期望顺序排列，然后添加任何不在期望列表中的新列
  const orderedKeys = [
    ...expectedOrder.filter(key => actualKeys.includes(key)),
    ...actualKeys.filter(key => !expectedOrder.includes(key))
  ]

  console.log('原始键顺序:', actualKeys)
  console.log('排序后键顺序:', orderedKeys)

  return orderedKeys.map((key) => {
    const value = firstRow[key]
    const isNumber = typeof value === 'number'
    return {
      title: key,
      dataIndex: key,
      align: isNumber ? 'right' : 'center',
      width: key === '日期' ? 140 : 120,
      // 数字类型显示格式化
      render: isNumber
        ? ({ record }: any) => {
            const val = record[key]
            return val ? Number(val).toFixed(2) : ''
          }
        : ({ record }: any) => {
            const val = record[key]
            return val || ''
          },
    } as TableInstanceColumns
  })
}

const {
  tableData: dataList,
  pagination,
  loading,
  search,
} = useTable((page) => toufangCustomerDailyPage({ customerId: customerId.value, ...page }), {
  immediate: false,
  paginationOption: {
    defaultPageSize: 5,
    defaultSizeOptions: [10, 20, 50, 100],
  },
})

// 监控数据变化并生成列配置
watch(dataList, async (newData) => {
  if (newData && newData.length > 0) {
    await nextTick()
    columns.value = generateColumns(newData)
    // 强制刷新表格组件
    tableKey.value++
  }
}, { immediate: true, deep: true })

const reset = () => {
  search()
}
const onOpen = async (id: string) => {
  customerId.value = id
  reset()
  visible.value = true
}

defineExpose({ onOpen })
</script>

<style scoped lang="scss"></style>
