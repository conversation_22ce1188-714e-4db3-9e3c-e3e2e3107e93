<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 1100 ? 1100 : '100%'"
    draggable
    :footer="false"
  >
    <div class="table-page">
      <GiTable
        :row-key="(record, index) => index"
        :data="dataList"
        :columns="columns"
        :loading="loading"
        :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
        :pagination="pagination"
        :disabled-tools="['size']"
        :disabled-column-keys="['name']"
        @refresh="search"
      >
      </GiTable>
    </div>
  </a-modal>
</template>

<script setup lang="tsx">
import { nextTick } from 'vue'
import { useWindowSize } from '@vueuse/core'
import { toufangCustomerDailyPage } from '@/apis/biz/customer'
import { useTable } from '@/hooks'
import type { TableInstanceColumns } from '@/components/GiTable/type'

const { width } = useWindowSize()

const visible = ref(false)
const title = computed(() => ('日报'))

const customerId = ref('')

const columns = ref<TableInstanceColumns[]>([])

// 生成动态列配置
const generateColumns = (data: any[]) => {
  if (!data || data.length === 0) return []

  const firstRow = data[0]
  return Object.keys(firstRow).map((key) => {
    const value = firstRow[key]
    const isNumber = typeof value === 'number'
    return {
      title: key,
      dataIndex: key,
      align: isNumber ? 'right' : 'center',
      width: key === '日期' ? 140 : 120,
      // 数字类型显示格式化
      render: isNumber
        ? ({ record }: any) => {
            const val = record[key]
            return val ? Number(val).toFixed(2) : '-'
          }
        : ({ record }: any) => {
            const val = record[key]
            return val || '-'
          },
    } as TableInstanceColumns
  })
}

const {
  tableData: dataList,
  pagination,
  loading,
  search,
} = useTable((page) => toufangCustomerDailyPage({ customerId: customerId.value, ...page }), {
  immediate: false
})

// 监控数据变化并生成列配置
watch(dataList, async (newData) => {
  console.log('dataList 变化:', newData)
  if (newData && newData.length > 0) {
    await nextTick()
    const newColumns = generateColumns(newData)
    console.log('生成的列配置:', newColumns)
    columns.value = newColumns
    console.log('columns 设置后的值:', columns.value)
  }
}, { immediate: true, deep: true })

const reset = () => {
  search()
}
const onOpen = async (id: string) => {
  customerId.value = id
  reset()
  visible.value = true
}

defineExpose({ onOpen })
</script>

<style scoped lang="scss"></style>
