<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 1100 ? 1100 : '100%'"
    draggable
    :footer="false"
  >
    <div class="table-page">
      <GiTable
        row-key="id"
        :data="dataList"
        :columns="columns"
        :loading="loading"
        :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
        :pagination="pagination"
        :disabled-tools="['size']"
        :disabled-column-keys="['name']"
        @refresh="search"
      >
      </GiTable>
    </div>
  </a-modal>
</template>

<script setup lang="tsx">
import { useWindowSize } from '@vueuse/core'
import { toufangCustomerDailyPage } from '@/apis/biz/customer'
import { useTable } from '@/hooks'
import type { TableInstanceColumns } from '@/components/GiTable/type'

const { width } = useWindowSize()

const visible = ref(false)
const title = computed(() => ('日报'))

const customerId = ref('')

const {
  tableData: dataList,
  pagination,
  loading,
  search,
} = useTable((page) => toufangCustomerDailyPage({ customerId: customerId.value, ...page }), { immediate: false })
const columns = ref<TableInstanceColumns[]>([
  { title: '日期', dataIndex: 'date', slotName: 'date', align: 'center', width: 120 },
  { title: '代理线', dataIndex: 'agentLine', slotName: 'agentLine', align: 'center', width: 120 },
  { title: '产品', dataIndex: 'product', slotName: 'product', align: 'center', width: 120 },
  { title: '打款', dataIndex: 'payment', slotName: 'payment', align: 'center', width: 120 },
  { title: '日消耗', dataIndex: 'dailyConsumption', slotName: 'dailyConsumption', align: 'center', width: 120 },
  { title: '回流', dataIndex: 'backflow', slotName: 'backflow', align: 'center', width: 120 },
  { title: '服务费', dataIndex: 'serviceFee', slotName: 'serviceFee', align: 'center', width: 120 },
  { title: '实际花费', dataIndex: 'actualCost', slotName: 'actualCost', align: 'center', width: 120 },
])

const reset = () => {
  search()
}
const onOpen = async (id: string) => {
  customerId.value = id
  reset()
  visible.value = true
}

defineExpose({ onOpen })
</script>

<style scoped lang="scss"></style>
