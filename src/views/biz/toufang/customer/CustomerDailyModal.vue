<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 1100 ? 1100 : '100%'"
    draggable
    :footer="false"
  >
    <div class="table-page">
      <GiTable
        :row-key="(record, index) => index"
        :data="dataList"
        :columns="columns"
        :loading="loading"
        :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
        :pagination="pagination"
        :disabled-tools="['size']"
        :disabled-column-keys="['name']"
        @refresh="search"
      >
      </GiTable>
    </div>
  </a-modal>
</template>

<script setup lang="tsx">
import { useWindowSize } from '@vueuse/core'
import { toufangCustomerDailyPage } from '@/apis/biz/customer'
import { useTable } from '@/hooks'
import type { TableInstanceColumns } from '@/components/GiTable/type'

const { width } = useWindowSize()

const visible = ref(false)
const title = computed(() => ('日报'))

const customerId = ref('')

const columns = ref<TableInstanceColumns[]>([])

// 生成动态列配置
const generateColumns = (data: any[]) => {
  if (!data || data.length === 0) return []

  const firstRow = data[0]
  return Object.keys(firstRow).map((key) => {
    const value = firstRow[key]
    const isNumber = typeof value === 'number'
    return {
      title: key,
      dataIndex: key,
      align: isNumber ? 'right' : 'center',
      width: key === '日期' ? 140 : 120,
      // 数字类型显示格式化
      render: isNumber
        ? ({ record }: any) => {
            const val = record[key]
            return val ? Number(val).toFixed(2) : '-'
          }
        : ({ record }: any) => {
            const val = record[key]
            return val || '-'
          },
    } as TableInstanceColumns
  })
}

const {
  tableData: dataList,
  pagination,
  loading,
  search,
} = useTable((page) => toufangCustomerDailyPage({ customerId: customerId.value, ...page }), {
  immediate: false,
  formatResult: (data) => {
    console.log('原始数据:', data)
    // 动态生成 columns
    if (data && data.length > 0) {
      const newColumns = generateColumns(data)
      console.log('生成的列配置:', newColumns)
      columns.value = newColumns
    }
    return data
  }
})

// 监控数据变化
watch(dataList, (newData) => {
  console.log('dataList 变化:', newData)
  console.log('columns 当前值:', columns.value)
}, { deep: true })

const reset = () => {
  search()
}
const onOpen = async (id: string) => {
  customerId.value = id
  reset()
  visible.value = true
}

defineExpose({ onOpen })
</script>

<style scoped lang="scss"></style>
