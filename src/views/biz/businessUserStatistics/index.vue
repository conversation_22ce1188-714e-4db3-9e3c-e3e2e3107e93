<template>
  <div class="table-page">
    <GiTable
      title="商务统计"
      row-key="id"
      :data="dataList"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
      :pagination="pagination"
      :disabled-tools="['size']"
      @refresh="search"
    >
      <template #toolbar-left>
        <a-select
          v-model="queryForm.businessUserId"
          placeholder="请选择商务"
          :options="userList"
          allow-clear
          allow-search
          style="width: 150px"
          @change="search"
        />
        <DateRangePicker v-model="queryForm.statTimes" @change="search" />
        <a-select
          v-model="queryForm.sortField"
          placeholder="排序字段"
          style="width: 120px; margin-right: 16px"
          allow-clear
          @change="search"
        >
          <a-option value="total_customer">累计客户数</a-option>
          <a-option value="total_paid">总打款</a-option>
          <a-option value="total_recharge">总充值</a-option>
          <a-option value="total_spend">总消耗</a-option>
          <a-option value="total_account">总户数</a-option>
          <a-option value="total_new_customer">新增客户数</a-option>
          <a-option value="total_refund_customer">退款客户数</a-option>
          <a-option value="occupied_amount">占用资金</a-option>
          <a-option value="dead_account">死户数量</a-option>
        </a-select>
        <a-select
          v-model="queryForm.ascSortFlag"
          placeholder="排序方向"
          allow-clear
          style="width: 120px; margin-right: 16px"
          @change="search"
        >
          <a-option :value="true">升序</a-option>
          <a-option :value="false">降序</a-option>
        </a-select>
      </template>
      <template #toolbar-right>
        <a-button @click="reset">
          <template #icon><icon-refresh /></template>
          <template #default>重置</template>
        </a-button>
        <a-button @click="onExport">
          <template #icon><icon-download /></template>
          <template #default>导出</template>
        </a-button>
      </template>

      <template #deadRate="{ record }">
        {{ record.deadRate ? `${record.deadRate}%` : '0%' }}
      </template>
    </GiTable>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { TableInstanceColumns } from '@/components/GiTable/type'
import { useDownload, useTable } from '@/hooks'
import { isMobile } from '@/utils'
import {
  type BusinessStatisticsQuery,
  exportBusinessStatistics,
  listBusinessStatistics,
} from '@/apis/biz/customerStatistics'
import { listUserDict } from '@/apis'
import type { LabelValueState } from '@/types/global'

defineOptions({ name: 'BusinessStatistics' })

// 用户列表状态
const userList = ref<LabelValueState[]>([])

// 获取用户列表方法
const getUserList = async () => {
  const { data } = await listUserDict()
  userList.value = data
}

// 查询表单
const queryForm = reactive<BusinessStatisticsQuery>({
  businessUserId: undefined,
  statTimes: undefined,
  ascSortFlag: false,
})

// 使用 useTable hook 处理表格数据
const {
  tableData: dataList,
  loading,
  pagination,
  search,
} = useTable((page) => listBusinessStatistics({ ...queryForm, ...page }), { immediate: true })

// 表格列定义
const columns = ref<TableInstanceColumns[]>([
  {
    title: '序号',
    width: 66,
    align: 'center',
    render: ({ rowIndex }) => h('span', {}, rowIndex + 1 + (pagination.current - 1) * pagination.pageSize),
    fixed: !isMobile() ? 'left' : undefined,
  },
  { title: '商务', dataIndex: 'nickname' },
  { title: '累计客户数', dataIndex: 'totalCustomer' },
  { title: '总打款', dataIndex: 'totalPaid' },
  { title: '总充值', dataIndex: 'totalRecharge' },
  { title: '总消耗', dataIndex: 'totalSpend' },
  { title: '总户数', dataIndex: 'totalAccount' },
  { title: '新增客户数', dataIndex: 'totalNewCustomer' },
  { title: '退款客户数', dataIndex: 'totalRefundCustomer' },
  { title: '总占用资金', dataIndex: 'occupiedAmount' },
  { title: '死户数量', dataIndex: 'deadAccount' },
  { title: '死户率', dataIndex: 'deadRate', slotName: 'deadRate' },
  { title: '平均户消耗', dataIndex: 'averageSpend' },
  { title: '平均户充值', dataIndex: 'averageRecharge' },
])

// 重置
const reset = () => {
  queryForm.businessUserId = undefined
  queryForm.statTimes = undefined
  queryForm.sortField = undefined
  queryForm.ascSortFlag = false
  search()
}

// 导出
const onExport = () => {
  useDownload(() => exportBusinessStatistics(queryForm))
}

getUserList()
</script>

  <style scoped lang="scss">
  .table-page {
    padding: 16px;

    :deep(.toolbar-left) {
      .arco-input-wrapper,
      .arco-input-number {
        margin-right: 16px;
      }
    }
  }
  </style>
