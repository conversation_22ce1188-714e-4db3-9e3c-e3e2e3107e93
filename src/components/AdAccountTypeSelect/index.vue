<template>
  <a-select
    placeholder="请选择广告户类型"
    :options="profitTypeList"
    allow-clear
    allow-search
    :multiple="multiple"
    style="width: 180px"
  />
</template>

<script setup lang="ts">
import type { LabelValueState } from '@/types/global'
import { listProfitTypeDict } from '@/apis'

defineOptions({ name: 'AdAccountTypeSelect' })

withDefaults(defineProps<Props>(), {
  multiple: false,
})

interface Props {
  multiple: boolean
}

const profitTypeList = ref<LabelValueState[]>([])

const getProfitTypeList = async () => {
  const { data } = await listProfitTypeDict({ adPlatform: '1', cat: 'ad_account' })
  profitTypeList.value = data
}

onMounted(() => {
  getProfitTypeList()
})
</script>

<style scoped lang="less"></style>
