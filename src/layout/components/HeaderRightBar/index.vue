<template>
  <div class="header-right-bar">
    <!-- CRM统计信息 -->
    <div v-if="crmStats" class="crm-stats">
      <!-- 线索统计 -->
      <div v-if="crmStats.showLeadCount" class="stat-card">
        <a-tooltip content="线索统计">
          <div class="stat-content">
            <div class="stat-header">
              <span class="stat-title">线索</span>
            </div>
            <div class="stat-numbers">
              <!-- <span class="stat-item pending">
                <span class="count">{{ crmStats.leadPendingCount }}</span>
                <span class="label">待跟进</span>
              </span> -->
              <span class="stat-item today">
                <span class="count" style="color:green;">{{ crmStats.leadTodayFollowCount }}</span>
                <span class="label">今日跟</span>
              </span>
              <span class="stat-item overdue">
                <span class="count" style="color:red;">{{ crmStats.leadOverdueCount }}</span>
                <span class="label">超时</span>
              </span>
            </div>
          </div>
        </a-tooltip>
      </div>

      <!-- 商机统计 -->
      <div v-if="crmStats.showOpportunityCount" class="stat-card">
        <a-tooltip content="商机统计">
          <div class="stat-content">
            <div class="stat-header">
              <span class="stat-title">商机</span>
            </div>
            <div class="stat-numbers">
              <!-- <span class="stat-item pending">
                <span class="count">{{ crmStats.opportunityPendingCount }}</span>
                <span class="label">待跟进</span>
              </span> -->
              <span class="stat-item today">
                <span class="count" style="color:green;">{{ crmStats.opportunityTodayFollowCount }}</span>
                <span class="label">今日跟</span>
              </span>
              <span class="stat-item overdue">
                <span class="count" style="color: red;">{{ crmStats.opportunityOverdueCount }}</span>
                <span class="label">超时</span>
              </span>
            </div>
          </div>
        </a-tooltip>
      </div>


     <!-- 回访统计 -->
      <div v-if="crmStats.showVisitTaskCount" class="stat-card">
        <a-tooltip content="回访任务统计">
          <div class="stat-content">
            <div class="stat-header">
              <span class="stat-title">回访</span>
            </div>
            <div class="stat-numbers">
              <span class="stat-item today">
                <span class="count" style="color:green;">{{ crmStats.visitTaskTodayFollowCount }}</span>
                <span class="label">今日跟</span>
              </span>
              <span class="stat-item overdue">
                <span class="count" style="color:red;">{{ crmStats.visitTaskOverdueCount }}</span>
                <span class="label">超时</span>
              </span>
            </div>
          </div>
        </a-tooltip>
      </div>
    </div>


    <!-- 原有的CV余额 -->
    <div>
      CV余额：{{ cvCardPlatformBalance }}
    </div>

    <!-- 项目配置 -->
    <a-tooltip content="项目配置" position="bl">
      <a-button size="mini" class="gi_hover_btn" @click="SettingDrawerRef?.open">
        <template #icon>
          <icon-settings :size="18" />
        </template>
      </a-button>
    </a-tooltip>

    <!-- 消息通知 -->
    <a-popover
      position="bottom"
      trigger="click"
      :content-style="{ marginTop: '-5px', padding: 0, border: 'none' }"
      :arrow-style="{ width: 0, height: 0 }"
    >
      <a-badge :count="unreadMessageCount" dot>
        <a-button size="mini" class="gi_hover_btn">
          <template #icon>
            <icon-notification :size="18" />
          </template>
        </a-button>
      </a-badge>
      <template #content>
        <Message @readall-success="getMessageCount" />
      </template>
    </a-popover>

    <!-- 全屏切换组件 -->
    <a-tooltip v-if="!isMobile()" content="全屏切换" position="bottom">
      <a-button size="mini" class="gi_hover_btn" @click="toggle">
        <template #icon>
          <icon-fullscreen v-if="!isFullscreen" :size="18" />
          <icon-fullscreen-exit v-else :size="18" />
        </template>
      </a-button>
    </a-tooltip>

    <!-- 暗黑模式切换 -->
    <a-tooltip content="主题切换" position="bottom">
      <GiThemeBtn></GiThemeBtn>
    </a-tooltip>

    <!-- 管理员账户 -->
    <a-dropdown trigger="hover">
      <a-row align="center" :wrap="false" class="user">
        <!-- 管理员头像 -->
        <Avatar :src="userStore.avatar" :name="userStore.nickname" :size="32" />
        <span class="username">{{ userStore.nickname }}</span>
        <icon-down />
      </a-row>
      <template #content>
        <a-doption @click="router.push('/setting/profile')">
          <span>个人中心</span>
        </a-doption>
        <a-divider :margin="0" />
        <a-doption @click="logout">
          <span>退出登录</span>
        </a-doption>
      </template>
    </a-dropdown>
  </div>
  <SettingDrawer ref="SettingDrawerRef"></SettingDrawer>
</template>

<script setup lang="ts">
import { Modal } from '@arco-design/web-vue'
import { useFullscreen } from '@vueuse/core'
import { onMounted, ref } from 'vue'
import Message from './Message.vue'
import SettingDrawer from './SettingDrawer.vue'
import { getCardPlatformBalance, getUnreadMessageCount } from '@/apis'
import { useUserStore } from '@/stores'
import { isMobile } from '@/utils'
import { getToken } from '@/utils/auth'
import { getCrmWorkbenchStats } from '@/apis/biz/crm/common.ts'

defineOptions({ name: 'HeaderRight' })
let socket: WebSocket
onBeforeUnmount(() => {
  if (socket) {
    socket.close()
  }
})

const unreadMessageCount = ref(0)
// 初始化 WebSocket
const initWebSocket = (token: string) => {
  socket = new WebSocket(`${import.meta.env.VITE_API_WS_URL}/websocket?token=${token}`)
  socket.onopen = () => {
    // console.log('WebSocket connection opened')
  }

  socket.onmessage = (event) => {
    unreadMessageCount.value = Number.parseInt(event.data)
  }

  socket.onerror = () => {
    // console.error('WebSocket error:', error)
  }

  socket.onclose = () => {
    // console.log('WebSocket connection closed')
  }
}

// 查询未读消息数量
const getMessageCount = async () => {
  const { data } = await getUnreadMessageCount()
  unreadMessageCount.value = data.total
  const token = getToken()
  if (token) {
    initWebSocket(token)
  }
}

const cvCardPlatformBalance = ref(0)

const getCardPlatformAmount = async (platform: string) => {
  const { data } = await getCardPlatformBalance(platform)
  cvCardPlatformBalance.value = data
}

const { isFullscreen, toggle } = useFullscreen()

const router = useRouter()
const userStore = useUserStore()
const SettingDrawerRef = ref<InstanceType<typeof SettingDrawer>>()

// 退出登录
const logout = () => {
  Modal.warning({
    title: '提示',
    content: '确认退出登录？',
    hideCancel: false,
    closable: true,
    onBeforeOk: async () => {
      try {
        await userStore.logout()
        await router.replace('/login')
        return true
      } catch (error) {
        return false
      }
    },
  })
}

onMounted(() => {
  getMessageCount()
  getCardPlatformAmount('2')
})

// CRM统计数据
const crmStats = ref<CrmWorkbenchStats | null>(null)
const crmStatsError = ref(false)

// 获取CRM统计数据
const getCrmStats = async () => {
  try {
    crmStatsError.value = false
    const { data } = await getCrmWorkbenchStats()
    crmStats.value = data
  } catch (error) {
    console.warn('获取CRM统计数据失败:', error)
    crmStatsError.value = true
    // 设置默认值，确保页面正常显示
    crmStats.value = {
      leadPendingCount: 0,
      leadTodayFollowCount: 0,
      leadOverdueCount: 0,
      opportunityPendingCount: 0,
      opportunityTodayFollowCount: 0,
      opportunityOverdueCount: 0,
      visitTaskTodayFollowCount: 0,
      visitTaskOverdueCount: 0,
      showLeadCount: false,
      showVisitTaskCount: false,
      showOpportunityCount: false
    }
  }
}

onMounted(() => {
  getCrmStats()
})
</script>

<style scoped lang="scss">
.arco-dropdown-open .arco-icon-down {
  transform: rotate(180deg);
}

.user {
  cursor: pointer;
  color: var(--color-text-1);

  .username {
    margin-left: 10px;
    white-space: nowrap;
  }

  .arco-icon-down {
    transition: all 0.3s;
    margin-left: 2px;
  }
}

.header-right-bar {
  display: flex;
  align-items: center;
  gap: 16px;
}

.crm-stats {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-card {
  background: var(--color-bg-2);
  border-radius: 6px;
  padding: 6px 20px;
  transition: all 0.2s;

  &:hover {
    background: var(--color-bg-3);
    border-color: var(--color-border-3);
  }

  .stat-content {
    .stat-header {
      display: flex;
      align-items: center;
      gap: 4px;
      margin-bottom: 4px;

      .stat-icon {
        font-size: 14px;
        color: var(--color-text-3);
      }

      .stat-title {
        font-size: 12px;
        color: var(--color-text-2);
        font-weight: 500;
      }
    }

    .stat-numbers {
      display: flex;
      gap: 8px;

      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        min-width: 32px;

        .count {
          font-size: 16px;
          font-weight: 600;
          line-height: 1;
        }

        .label {
          font-size: 10px;
          line-height: 1;
          margin-top: 2px;
          opacity: 0.8;
        }

        &.pending {
          .count {
            color: var(--color-warning-6);
          }
          .label {
            color: var(--color-warning-5);
          }
        }

        &.today {
          .count {
            color: var(--color-primary-6);
          }
          .label {
            color: var(--color-primary-5);
          }
        }

        &.overdue {
          .count {
            color: var(--color-danger-6);
          }
          .label {
            color: var(--color-danger-5);
          }
        }
      }
    }
  }
}
</style>
